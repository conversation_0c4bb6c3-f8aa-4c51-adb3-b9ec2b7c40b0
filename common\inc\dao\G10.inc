<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : G10_top_notices
	class G10_top_notices extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function G10G01($group_chat_id, $target_menu, $target_menu_id) {
			$group_chat_id = intval($group_chat_id);
			$target_menu = intval($target_menu);
			$target_menu_id = intval($target_menu_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('target_menu='.$target_menu);
			$sel->addcond('target_menu_id='.$target_menu_id);
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function G10S01($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- 複数のメニューがある際は再検討（union？） -----
		function G10S02($group_chat_id, $target_menu) {
			$group_chat_id = intval($group_chat_id);
			$target_menu = intval($target_menu);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('G10.group_chat_id='.$group_chat_id);
			$sel->addcond('G10.target_menu='.$target_menu);
			$sel->addcond('G10.is_end=0');
			$sel->addcond('G10.Remove=0');
			if ( $target_menu == 1 ) {
				// ----- スケジュール -----
				$sel->addfield('E12.schedule_code');
				$sel->addfield('E12.schedule_title');
				$sel->addinner('E12_schedules E12', 'E12.schedule_id=G10.target_menu_id');
			}
			return $this->loadList($sel);
		}
		// ----- グループチャット有効期限 -----
		function G10S03() {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('E12.schedule_close_date');
			$sel->addfield('E12.schedule_close_time');
			$sel->addcond('G10.target_menu=1');
			$sel->addcond('G10.is_end=0');
			$sel->addcond('G10.Remove=0');
			$sel->addinner('E12_schedules E12', 'E12.schedule_id=G10.target_menu_id');
			$sel->addcond('E12.schedule_close_date>0');
			$sel->addcond('E12.schedule_close_date<='.date('Ymd'));
			$sel->addcond('E12.schedule_close_date>='.date('Ymd', mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))));
			$sel->addcond('E12.target_menu=2');
			$sel->addcond('E12.Remove=0');
			return $this->loadList($sel);
		}
		// ----- 入力チェック -----
		function G10E01($dataG10) {
			InputCheck($dataG10['group_chat_id'], 'グループチャットID', 'N', 'N', 9, 0);
			InputCheck($dataG10['target_menu'], '対象メニュー', 'N', 'N', 9, 0);
			InputCheck($dataG10['target_menu_id'], '対象メニュID', 'N', 'N', 9, 0);
			InputCheck($dataG10['is_end'], '終了区分', '', 'N', 1, 0);
		}
		// -------------------------------------------------
		// 更新
		function G10U01($dataG10, $creator) {
			return $this->updateAllField($dataG10, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function G10R01($group_chat_id, $target_menu, $target_menu_id, $creator) {
			$group_chat_id = intval($group_chat_id);
			$target_menu = intval($target_menu);
			$target_menu_id = intval($target_menu_id);
			$dataG10 = $this->CreateNewDto();
			$dataG10['group_chat_id'] = $group_chat_id;
			$dataG10['target_menu'] = $target_menu;
			$dataG10['target_menu_id'] = $target_menu_id;
			return $this->removeRecord($dataG10, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('group_chat_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('target_menu', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('target_menu_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('is_end', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('G10_top_notices', 'G10');
			// join E12
			$this->addFieldDef('schedule_id', DFNUM, 0);
			$this->addFieldDef('schedule_code', DFSTR, 0);
			$this->addFieldDef('schedule_title', DFSTR, 0);
			$this->addFieldDef('schedule_close_date', DFNUM, 0);
			$this->addFieldDef('schedule_close_time', DFNUM, 0);
		}
	}
	$dbG10 = new G10_top_notices();
