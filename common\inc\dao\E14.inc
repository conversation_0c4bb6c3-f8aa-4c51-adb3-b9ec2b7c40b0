<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : E14_schedule_applies
	class E14_schedule_applies extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function E14G01($schedule_id, $schedule_candidate_id, $user_id) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('schedule_candidate_id='.$schedule_candidate_id);
			$sel->addcond('user_id='.$user_id);
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function E14S01($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		function E14S02($schedule_id, $schedule_candidate_id) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addcond('E14.schedule_id='.$schedule_id);
			$sel->addcond('E14.schedule_candidate_id='.$schedule_candidate_id);
			$sel->addcond('E14.Remove=0');
			$sel->addinner('U01_users U01', 'U01.user_id=E14.user_id');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=E14.user_id and U02.image_id = 1 and U02.Remove=0');
			$sel->addorder('E14.apply');
			return $this->loadList($sel);
		}
		function E14C01($schedule_id, $schedule_candidate_id, $apply) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$apply = intval($apply);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('schedule_candidate_id='.$schedule_candidate_id);
			$sel->addcond('apply='.$apply);
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(DISTINCT(user_id))');
		}
		function E14C02($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(DISTINCT(user_id))');
		}
		// ----- 性別 -----
		function E14C03($schedule_id, $user_sex) {
			$schedule_id = intval($schedule_id);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('E14.schedule_id='.$schedule_id);
			$sel->addcond('E14.Remove=0');
			$sel->addinner('U01_users U01', 'U01.user_id=E14.user_id');
			$sel->addcond('U01.user_sex='.$user_sex);
			$sel->addcond('U01.Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(DISTINCT(E14.user_id))');
		}
		function E14C04($schedule_id, $schedule_candidate_id, $apply, $user_sex) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$apply = intval($apply);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('E14.schedule_id='.$schedule_id);
			$sel->addcond('E14.schedule_candidate_id='.$schedule_candidate_id);
			$sel->addcond('E14.apply='.$apply);
			$sel->addcond('E14.Remove=0');
			$sel->addinner('U01_users U01', 'U01.user_id=E14.user_id');
			$sel->addcond('U01.user_sex='.$user_sex);
			$sel->addcond('U01.Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(DISTINCT(E14.user_id))');
		}
		// ----- 入力チェック -----
		function E14E01($dataE14) {
			InputCheck($dataE14['schedule_id'], 'スケジュールID', 'N', 'N', 9, 0);
			InputCheck($dataE14['schedule_candidate_id'], '候補日ID', 'N', 'N', 9, 0);
			InputCheck($dataE14['user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataE14['apply'], '応募', 'N', 'N', 1, 0);
		}
		// -------------------------------------------------
		// 更新
		function E14U01($dataE14, $creator) {
			return $this->updateAllField($dataE14, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function E14R01($schedule_id, $schedule_candidate_id, $user_id, $creator) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$user_id = intval($user_id);
			$dataE14 = $this->CreateNewDto();
			$dataE14['schedule_id'] = $schedule_id;
			$dataE14['schedule_candidate_id'] = $schedule_candidate_id;
			$dataE14['user_id'] = $user_id;
			return $this->removeRecord($dataE14, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('schedule_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('schedule_candidate_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('apply', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('E14_schedule_applies','E14');
			// join U01
			$this->addFieldDef('user_code', DFSTR, 0);
			$this->addFieldDef('user_nickname', DFSTR, 0);
			$this->addFieldDef('user_sex', DFNUM, 0);
			// join U02
			$this->addFieldDef('image_id', DFNUM, 0);
			$this->addFieldDef('image_code', DFSTR, 0);
			$this->addFieldDef('image_extension', DFSTR, 0);
			$this->addFieldDef('version_id', DFSTR, 0);
			$this->addFieldDef('publish_version_id', DFSTR, 0);
			$this->addFieldDef('thumbnail_version_id', DFSTR, 0);
		}
	}
	$dbE14 = new E14_schedule_applies();
