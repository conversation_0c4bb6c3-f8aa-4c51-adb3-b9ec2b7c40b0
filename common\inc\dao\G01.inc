<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : G01_group_chats
	class G01_group_chats extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function G01G01($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			return $this->loadRecord($sel);
		}
		// ----- codeで1件 -----
		function G01G02($group_chat_code) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_code="'.$group_chat_code.'"');
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- 自分の情報を取得 -----
		function G01G03($group_chat_code, $user_id) {
			$user_id = intval($user_id);	// 自身のid
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('G02.is_certificated');
			$sel->addfield('G02.is_manager');
			$sel->addcond('G01.group_chat_code="'.$group_chat_code.'"');
			$sel->addcond('G01.Remove=0');
			$sel->addinner('G02_group_chat_users G02','G02.group_chat_id=G01.group_chat_id');
			$sel->addcond('G02.user_id='.$user_id);
			$sel->addcond('G02.Remove=0');
			return $this->loadRecord($sel);
		}
		function G01G04($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['group_chat_id']) ) {
				$sel->addcond('group_chat_id='.$searchList['group_chat_id']);
			}
			if ( !empty($searchList['is_delete']) ) {
				if ( $searchList['is_delete'] == 9 ) $searchList['is_delete'] = 0;
				$sel->addcond('is_delete='.$searchList['is_delete']);
			}
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function G01S01($dataU01, $searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('G02.user_id as G02_user_id');
			$sel->addfield('G02.is_certificated');
			$sel->addcond('G01.Remove=0');
			$sel->addcond('(G01.sex_id='.$dataU01['user_sex'].' or G01.sex_id=9)');
			$sel->addorder('G01.last_chat_datetime DESC');
			if ( !empty($searchlist['limit']) ) {
				$sel->setoffset(($searchList['page'] - 1) * $searchlist['limit']);
				$sel->setlimit($searchlist['limit']);
			}
			$sel->addleftjoin('G02_group_chat_users G02', 'G02.group_chat_id=G01.group_chat_id and G02.user_id=' . $dataU01['user_id'] . ' and G02.Remove=0');
			// ----- 非表示除外 -----
			$sel->addleftjoin('(select group_chat_id from G07_hide_group_chats where user_id = ' . $dataU01['user_id'] . ') G07','G07.group_chat_id=G01.group_chat_id');
			$sel->addcond('G07.group_chat_id IS NULL');
			return $this->loadList($sel);
		}
		// ----- 参加中リスト -----
		function G01S02($dataU01, $includeDelete=0) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('G02.is_manager');
			if (empty($includeDelete)) {
				$sel->addcond('G01.is_delete=0');
			}
			$sel->addcond('G01.Remove=0');
			$sel->addorder('G01.last_chat_datetime DESC');
			$sel->addinner('G02_group_chat_users G02', 'G02.group_chat_id=G01.group_chat_id');
			$sel->addcond('G02.user_id='.$dataU01['user_id']);
			$sel->addcond('(G02.is_certificated=0 or G02.is_certificated=1)');
			$sel->addcond('G02.Remove=0');
			// ----- 非表示除外 -----
			$sel->addleftjoin('(select group_chat_id from G07_hide_group_chats where user_id = ' . $dataU01['user_id'] . ') G07','G07.group_chat_id=G01.group_chat_id');
			$sel->addcond('G07.group_chat_id IS NULL');
			return $this->loadList($sel);
		}
		// ----- 未参加リスト -----
		function G01S03($dataU01, $searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['keyword']) ) {
				if( preg_match( '/^[0-9]+$/', $searchList['keyword'] ) ) {
					// 数字のみ
					$searchList['keyword'] = intval($searchList['keyword']);
					$sel->addcond('G01.group_chat_id='.$searchList['keyword']);
				} else {
					// あいまい検索
					$condkeyword = seach_split("concat(G01.group_chat_title, G01.group_chat_text)", $searchList['keyword'], 'and');
					$sel->addcond($condkeyword);
				}
			}
			$sel->addcond('(G01.sex_id='.$dataU01['user_sex'].' or G01.sex_id=9)');
			$sel->addcond('G01.is_delete=0');
			$sel->addcond('G01.Remove=0');
			$sel->addorder('G01.last_chat_datetime DESC');
			if ( !empty($searchList['limit']) ) {
				$sel->setoffset(($searchList['page'] - 1) * $searchList['limit']);
				$sel->setlimit($searchList['limit']);
			}
			$sel->addleftjoin('(select group_chat_id, user_id, is_certificated from G02_group_chat_users where user_id = ' . $dataU01['user_id'] . ') G02','G02.group_chat_id=G01.group_chat_id and G02.is_certificated<2');
			$sel->addcond('G02.user_id IS NULL');
			// ----- 非表示除外 -----
			$sel->addleftjoin('(select group_chat_id from G07_hide_group_chats where user_id = ' . $dataU01['user_id'] . ') G07','G07.group_chat_id=G01.group_chat_id');
			$sel->addcond('G07.group_chat_id IS NULL');
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $dataU01['user_id'] . ') U24','U24.block_user_id=G01.user_id');
			$sel->addcond('U24.block_user_id IS NULL');
			return $this->loadList($sel);
		}
		// ----- 管理画面用リスト -----
		function G01S04($searchList) {
			$sel = $this->CreateSSql(TRUE);
			$this->addcondition($sel, $searchList);
			$sel->addorder('G01.last_chat_datetime DESC');
			if ( !empty($searchList['limit']) ) {
				$sel->setoffset(($searchList['page'] - 1) * $searchList['limit']);
				$sel->setlimit($searchList['limit']);
			}
			return $this->loadList($sel);
		}
		// ----- 画像削除リスト -----
		function G01S05($last_chat_datetime) {
			$last_chat_datetime = intval($last_chat_datetime);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('last_chat_datetime<' . $last_chat_datetime);
			$sel->addcond('is_delete>0');
			$sel->addcond('is_image_delete=0');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- 未参加件数 -----
		function G01C01($dataU01, $searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['keyword']) ) {
				$searchList['keyword'] = intval($searchList['keyword']);
				$sel->addcond('G01.group_chat_id='.intval($searchList['keyword']));
			}
			$sel->addcond('(sex_id='.$dataU01['user_sex'].' or sex_id=9)');
			$sel->addcond('Remove=0');
			$sel->addleftjoin('(select group_chat_id, user_id from G02_group_chat_users where user_id = ' . $dataU01['user_id'] . ') G02','G02.group_chat_id=G01.group_chat_id');
			$sel->addcond('G02.user_id IS NULL');
			// ----- 非表示除外 -----
			$sel->addleftjoin('(select group_chat_id from G07_hide_group_chats where user_id = ' . $dataU01['user_id'] . ') G07','G07.group_chat_id=G01.group_chat_id');
			$sel->addcond('G07.group_chat_id IS NULL');
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $dataU01['user_id'] . ') U24','U24.block_user_id=G01.user_id');
			$sel->addcond('U24.block_user_id IS NULL');
			return $this->loadCalcResult($sel, 'COUNT(G01.group_chat_id)');
		}
		// ----- 管理画面件数 -----
		function G01C02($searchList=[]) {
			$sel = $this->CreateSSql(FALSE);
			$this->addcondition($sel, $searchList);
			return $this->loadCalcResult($sel, 'COUNT(group_chat_id)');
		}
		// ----- 抽出条件 -----
		private function addcondition(&$sel, $searchList) {
			if ( !empty($searchList['keyword']) ) {
				//キーワード検索（スペースで分割して、and検索）
				$condkeyword = seach_split("concat(G01.group_chat_title, G01.group_chat_text)", $searchList['keyword'], 'and');
				$sel->addcond($condkeyword);
			}
			if ( !empty($searchList['not_delete']) ) {
				$sel->addcond('G01.is_delete=0');
				$sel->addcond('G01.Remove=0');
			}
		}
		// ----- 入力チェック -----
		function G01E01($dataG01) {
			InputCheck($dataG01['group_chat_id'], 'グループチャットID', '', 'N', 9, 0);
			InputCheck($dataG01['group_chat_code'], 'グループチャットコード', '', 'H', 20, 0);
			InputCheck($dataG01['user_id'], '会員ID', '', 'N', 9, 0);
			InputCheck($dataG01['sex_id'], '参加者の性別', 'N', 'N', 1, 0);
			InputCheck($dataG01['is_certification'], '承認区分', '', 'N', 1, 0);
			InputCheck($dataG01['is_application_comment'], '申請コメント区分', '', 'N', 1, 0);
			InputCheck($dataG01['group_chat_title'], 'タイトル', 'N', '', 0, 80);
			InputCheck($dataG01['group_chat_text'], 'グループチャット目的', 'N', '', 0, 30000);
			InputCheck($dataG01['event_id'], 'イベントID', '', 'N', 9, 0);
			InputCheck($dataG01['user_count'], '参加人数', '', 'N', 9, 0);
			InputCheck($dataG01['last_chat_datetime'], '最終コメント日時', '', 'N', 14, 0);
			InputCheck($dataG01['chat_count'], 'コメント数', '', 'N', 9, 0);
			InputCheck($dataG01['is_delete'], '削除区分', '', 'N', 1, 0);
			InputCheck($dataG01['is_image_delete'], '画像削除区分', '', 'N', 1, 0);
		}
		// -------------------------------------------------
		// 更新
		function G01U01($dataG01, $creator) {
			return $this->updateAutoIncrementAllField($dataG01, $creator);
		}
		// -------------------------------------------------
		// 更新（参加人数）
		function G01U02($dataG01) {
			$sql = new query;
			$sql->addsql('update G01_group_chats set user_count='.$dataG01['user_count'].' where group_chat_id='.$dataG01['group_chat_id']);
			$mainDB = MainDB();
			$sql->exec($mainDB);
		}
		// -------------------------------------------------
		// 論理削除
		function G01R01($group_chat_id, $creator) {
			$group_chat_id = intval($group_chat_id);
			$dataG01 = $this->CreateNewDto();
			$dataG01['group_chat_id'] = $group_chat_id;
			return $this->removeRecord($dataG01, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('group_chat_id', DFNUM, FTREAD|FTID);
			$this->addFieldDef('group_chat_code', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('sex_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_certification', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_application_comment', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('group_chat_title', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('group_chat_text', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('memo',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('event_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('user_count', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('last_chat_datetime', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('chat_count', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_delete', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_image_delete', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('G01_group_chats','G01');
			// join G02
			$this->addFieldDef('G02_user_id', DFNUM, 0);
			$this->addFieldDef('is_certificated', DFNUM, 0);
			$this->addFieldDef('is_manager', DFNUM, 0);
		}
	}
	$dbG01 = new G01_group_chats();
