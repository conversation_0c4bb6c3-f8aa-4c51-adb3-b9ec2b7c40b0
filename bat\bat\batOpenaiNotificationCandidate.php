<?php
	ini_set('error_log', '/var/log/batch.log');
	include("_filepath.inf");
	include_once($gINC.'_global.inc');
	include_once($gLIB.'_pubfunc.inc');
	include_once($gINC.'_healmatefunc.inc');
	include_once($gINC.'logic/openai.inc');
	include_once($gINC.'dao/B06.inc');
	include_once($gINC.'dao/B07.inc');
	include_once($gINC.'dao/B09.inc');
	include_once($gINC.'dao/B14.inc');
	include_once($gINC.'dao/M47.inc');
error_log(basename(__FILE__).' start');
	//----------------------------------------------------------
	// php -q /var/www/bat/batOpenaiNotificationCandidate.php
	// 新しい問合せを取得
	// 新しい問合せの質問の内容について、ベクトルを求める（getEmbedding）
	// 蓄積しているvector DBから類似の回答を取得して候補として表示
	//----------------------------------------------------------

	$searchList = array();
	$searchList['is_reply'] = 0; //未対応
	$searchList['exclude_auto'] = 1; //1:自動応答は除く
	$searchList['is_no_comment'] = 1; //1:返信のみ
	$searchList['not_administrator_id'] = 1; //1:管理者からの送信は除く
	$searchList['page'] = 1;
	$searchList['limit'] = 10;

	$listB06 = $dbB06->B06S02($searchList);
	$openai = new OpenAI();
	error_log(basename(__FILE__).' cnt='.count($listB06));

	//タグ設定
	foreach ( $listB06 as $dataB06 ) {
		$chkB14 = $dbB14->B14C02($dataB06['notification_id']);
		if ( !empty($chkB14) ) {
			//すでにタグ設定あればスキップ
			error_log(basename(__FILE__).__LINE__.'|B14 skip');
			continue;
		}
		$notification_text = mbTrim($dataB06['notification_text']);
		if ( empty($notification_text) ) {
			//質問なければスキップ
			error_log(basename(__FILE__).__LINE__.'|no question skip');
			continue;
		}
		//タグ検索
		$question = $dataB06['notification_title']."\n".$dataB06['notification_text'];
		error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($question));
		foreach( $listM47 as $dataM47 ) {
			if ( strpos($question, $dataM47['notification_tag_name'])  !== false ) {
				$dataB14 = $dbB14->CreateNewDto();
				$dataB14['notification_id'] = $dataB06['notification_id'];
				$dataB14['notification_tag_id'] = $dataM47['notification_tag_id'];
				$dbB14->B14U01($dataB14, basename(__FILE__, ".php"));
			}
		}
	}
	foreach ( $listB06 as $dataB06 ) {
		if (!empty($dataB06['is_reply'])) {
			//未対応以外はスキップ
			error_log(basename(__FILE__).__LINE__.'|is_reply skip');
			continue;
		}
		if (!empty($dataB06['comment_count'])) {
			//回答あればスキップ
			error_log(basename(__FILE__).__LINE__.'|comment_count skip');
			continue;
		}
		$chkB09 = $dbB09->B09G02($dataB06['notification_id']);
		if ( !empty($chkB09['notification_id']) ) {
			//すでに下書きあればスキップ
			error_log(basename(__FILE__).__LINE__.'|B09 skip');
			continue;
		}
		$notification_text = mbTrim($dataB06['notification_text']);
		if ( empty($notification_text) ) {
			//質問なければスキップ
			error_log(basename(__FILE__).__LINE__.'|no question skip');
			continue;
		}
		//候補検索
		$answer='';
		$commentData=[];
		try {
			$question = $dataB06['notification_title']."\n".$dataB06['notification_text'];
			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($question));

			$param = array(
				'question' => $question,
				'limit' => 20, //推論候補数
				'exclude_db' => 0, //0:類似検索を実施する
				'exclude_ai' => 1, //1:AIプロンプト実施しない
			);
	
			$result = $openai->getAnswerFromOpenAI($param);
			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($result));

			if (!empty($result['result_from_ai'])) {
				$answer .="\n【AI回答】\n";
				$answer .= $result['result_from_ai'];
			}
			if (!empty($result['result_from_db'])) {
				foreach ($result['result_from_db'] as $data) {
					$refer_link='';
					$refer_name='';
					switch ($data['refer_kind']) {
						case 1:
							$refer_link=$gURLROOT.'admin/masterreplymailtemplateedit?id='.$data['refer_id'];
							$refer_name='メール文例';
							break;
						case 2:
							$refer_link=$gURLROOT.'admin/usernotificationcommentlist?id='.$data['refer_id'];
							$dataB07 = $dbB07->B07G03($data['refer_id']);
							$refer_name=$dataB07['administrator_familyname'].' '.$dataB07['administrator_givenname'];
							break;
					}
					$commentData[] = [
						'question' => $data['question'],
						'answer'   => $data['answer'],
						'cosine_similarity'   => $data['cosine_similarity'],
						'refer_link'   => $refer_link, //リンク（メール文例、過去事例）
						'refer_name'   => $refer_name, //回答者
					];
				}
			}
		} catch (Exception $e) {
			error_log(basename(__FILE__).__LINE__.'eroor|'.var_dump_text($e->getMessage()));
		}
		$dataB09 = $dbB09->CreateNewDto();
		$dataB09['notification_id'] = $dataB06['notification_id'];
		$dataB09['administrator_id'] = $gSYSTEM_ADMIN_ID;
		$dataB09['comment_text'] = json_encode($commentData, JSON_UNESCAPED_UNICODE);
		$dbB09->B09U01($dataB09, basename(__FILE__, ".php"));
	}
error_log(basename(__FILE__).' finish');
	exit();
