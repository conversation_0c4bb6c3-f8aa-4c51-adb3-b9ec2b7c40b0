<?php
	ini_set('error_log', '/var/log/batch.log');
	include("_filepath.inf");
	include_once($gINC.'_global.inc');
	include_once($gLIB.'_pubfunc.inc');
	include_once($gINC.'_healmatefunc.inc');
	include_once($gINC.'dao/U01.inc');
	include_once($gINC.'dao/U03.inc');
	include_once($gINC.'dao/U08.inc');
	include_once($gINC.'dao/U10.inc');
	include_once($gINC.'dao/U28.inc');
	include_once($gINC.'dao/U42.inc');
	include_once($gINC.'dao/U51.inc');
	include_once($gINC.'dao/U52.inc');
	include_once($gINC.'dao/P01.inc');
	include_once($gINC.'dao/W06.inc');
	include_once($gINC.'dao/M01.inc');
	include_once($gINC.'dao/M02.inc');
	include_once($gINC.'dao/M03.inc');
	include_once($gINC.'dao/M04.inc');
	include_once($gINC.'dao/M05.inc');
	include_once($gINC.'dao/M06.inc');
	include_once($gINC.'dao/M07.inc');
	include_once($gINC.'dao/M08.inc');
	include_once($gINC.'dao/M09.inc');
	include_once($gINC.'dao/M10.inc');
	include_once($gINC.'dao/M11.inc');
	include_once($gINC.'dao/M12.inc');
	include_once($gINC.'dao/M13.inc');
	include_once($gINC.'dao/M14.inc');
	include_once($gINC.'dao/M15.inc');
	include_once($gINC.'dao/M16.inc');
	include_once($gINC.'dao/M29.inc');
	include_once($gINC.'dao/M30.inc');
	include_once($gINC.'dao/M39.inc');
	include_once($gINC.'dao/M40.inc');
	include_once($gINC.'dao/M42.inc');
	include_once($gINC.'dao/W07.inc');
	include_once($gINC.'logic/mail.inc');
	include_once($gINC.'logic/util.inc');
	include_once($gINC.'_ocr.inc');
	error_log('batIdentifyConfirmAuto start');
	// ====== 変数  ======
	$LONG_FORCE_PRIVATE = "長期保留";
	$OCR_ERROR = 99999999;

	// ====== 保留・本人確認未承認  ======
	$searchList = array();
	$searchList['is_identify_check'] = 1;
	$searchList['user_status'] = 1; //1：稼働中
	$searchList['un_scam'] = 99;
	$listU01 = $dbU01->U01S03($searchList);
	error_log(basename(__FILE__).__LINE__.'cnt|'.count($listU01));
	$cnt_red = 0;
	$cnt_yellow = 0;
	$cnt_ipaddress = 0;
	$cnt_normal = 0;
	$cnt_media = 0;
	$cnt_ocr = 0;
	$cnt_ocr_ok = 0;
	$scam_comment;
	$total = count($listU01);
	$listM30 = $dbM30->M30S03(3); //3:プロフィール（アラートチェック）
	$listbatchM30 = $dbM30->M30S03(3, 1); //3:プロフィール・バッチのみ（詐欺候補チェック）
	$listSkipWord = ["怪しい","詐欺"]; //公開しない（備考の文言チェック）
	$listExceptDescription = [
		"長期保留",
		"【前回】",
		"【前回】\n",
		"怪しい",
		"複数アカウント疑い",
		"【PC】",
		"【前回アカウントあり】",
		"（前回アカウントあり）",
		"【複数アカウント】疑い",
		"年齢詐称疑い（メールアドレス）",
		"【イエローカード】あり",
		"【強制退会】あり",
		"年齢詐称（前回差異）",
		"前回マッチングなし",
		"【経路不明】",
		"出戻り",
		"OCR:OK",
		"OCR:NG",
		"OCR:NG（不一致）",
		"性別詐称",
		"性別詐称疑い",
		"（前回差異）",
		"（メールアドレス）",
		",",
		" ",
		"　",
		",LINE",
		"媒体:google",
		"媒体:Google",
		"媒体:magazine",
		"媒体:カミングアウト東京",
		"媒体:Twitter",
		"媒体:アドネットワーク",
		"媒体:アフィリエイト",
		"媒体:Yahoo",
		"媒体:ラブマ",
		"媒体:ポコチェ",
		"媒体:スポニチafn",
		"媒体:女性自身",
		"媒体:注目情報はこれだ2023",
		"媒体:Twitter きしょくて痛い男のLINE",
		"媒体:お友達紹介",
		"媒体:Twitter公式",
		"媒体:Instagram",
		"媒体:自社アフィリエイト",
		"媒体:ティッシュ配り_銀座",
		"媒体:ティッシュ配り_品川",
		"媒体:ティッシュ配り_新宿",
		"媒体:Yahoo!リターゲティング",
		"媒体:トイレ動画_男性",
		"媒体:トイレ動画_女性",
		"媒体:トイレ動画_男女共通",
		"媒体:X広告",
		"媒体:アンケートアド",
		"媒体:Facebook広告",
		"媒体:Instagram広告",
		"媒体:マイナビ記事/座談会",
		"媒体:JCAST記事/インタビュー",
		"媒体:YouTube_岡野あつこコラボ",
		"媒体:Google広告リタゲ",
		"媒体:ブランドLP_Facebook広告",
		"媒体:ブランドLP_Instagram広告",
		"媒体:ブランドLP_Threads広告",
	]; //除外文字

	foreach ( $listU01 as $dataU01 ) {
		$isSkip = false;
		foreach ( $listSkipWord as $skipWord ) {
			if ( strpos($dataU01['user_description'], $skipWord) !== false ) {
				$isSkip = true;
			}
		}
		if ($isSkip) {
			continue;
		}
		$checkOcr = false;
		$scam_comment='';
		$chkList = array();
		$chkList['except_self'] = true;
		$chkList['user_id'] = $dataU01['user_id'];
		$chkList['user_mailaddress'] = $dataU01['user_mailaddress'];
		$chkList['user_status'] = 10; //強制退会
		$mailaddresscountforce = $dbU01->U01C01($chkList);
		$chkList = array();
		$chkList['except_self'] = true;
		$chkList['user_id'] = $dataU01['user_id'];
		$chkList['user_mailaddress'] = $dataU01['user_mailaddress'];
		$chkList['is_match'] = true; //マッチング
		$mailaddresscountmatch = $dbU01->U01C13($chkList);
		$chkList = array();
		$chkList['except_self'] = true;
		$chkList['user_id'] = $dataU01['user_id'];
		$chkList['user_mailaddress'] = $dataU01['user_mailaddress'];
		$mailaddresscount = $dbU01->U01C13($chkList);
		$searchList = array();
		$searchList['except_self'] = true;
		$searchList['user_id'] = $dataU01['user_id'];
		$searchList['user_mailaddress'] = $dataU01['user_mailaddress'];
		$searchList['yellow_card'] = true; //イエローカード
		$yellowmailaddresscount = $dbU01->U01C01($searchList);
		$searchList = array();
		$searchList['except_self'] = true;
		$searchList['user_id'] = $dataU01['user_id'];
		$searchList['user_status'] = 1; //稼働中
		$searchList['user_birthday'] = $dataU01['user_birthday'];
		$searchList['user_sex'] = $dataU01['user_sex'];
		$searchList['ipaddress'] = $dataU01['Iuser']; //IPアドレス
		$ipaddresscount = $dbU01->U01C01($searchList);
		//前回登録とチェック
		$difflastuserinfo=0;
		$lastdataU52 = $dbU52->U52G01($dataU01['user_id'], 1);
		if ( !empty($lastdataU52['user_id']) ) {
			$dataU52 = $dbU52->U52G02($lastdataU52['user_id'], 1);
			if ( !empty($dataU52['user_id'])) {
				if ( !empty($dataU52['user_description'])) {
					//前回の備考を追加
					$replacedDescriptions = $dataU52['user_description'];
					foreach ($listExceptDescription as $desc) {
						$replacedDescriptions = str_replace($desc, "", $replacedDescriptions);
					}
					$replacedDescriptions = mbTrim($replacedDescriptions);
					if ( !empty($replacedDescriptions)) {
						$replacedDescriptions .= "\n";
						chkAddWord($scam_comment, "【前回】\n", $dataU01['user_description']);
						chkAddWord($scam_comment, $replacedDescriptions, $dataU01['user_description']);
					}
				}
				if ( $dataU52['user_status'] == 1) {
					chkAddWord($scam_comment, "複数アカウント疑い【前回アカウントあり】", $dataU01['user_description']);
					$difflastuserinfo++;
				}
				if ( $dataU52['user_sex'] != $dataU01['user_sex'] ) {
					chkAddWord($scam_comment, "性別詐称（前回差異）", $dataU01['user_description']);
					$difflastuserinfo++;
				}
				if ( $dataU52['user_birthday'] != $dataU01['user_birthday'] ) {
					chkAddWord($scam_comment, "年齢詐称（前回差異）", $dataU01['user_description']);
					$difflastuserinfo++;
				}
			}
		}
		//以前のメールアドレスとチェック
		$diffsamemailinfo=0;
		$searchList = array();
		$searchList['except_self'] = true;
		$searchList['user_id'] = $user_id;
		$searchList['identify_confirm'] = 1;
		$searchList['user_mailaddress'] = $dataU01['user_mailaddress'];
		$listU01 = $dbU01->U01S03($searchList);
		$last_descriptions = '';
		foreach ( $listU01 as $chkU01 ) {
			//前回の備考を追加
			if (empty($last_descriptions)) {
				$last_descriptions = $chkU01['user_description'];
				foreach ($listExceptDescription as $desc) {
					$last_descriptions = str_replace($desc, "", $last_descriptions);
				}
				$last_descriptions = mbTrim($last_descriptions);
			}
			if (!empty($last_descriptions)) {
				$last_descriptions .= "\n";
				chkAddWord($scam_comment, "【前回】\n", $dataU01['user_description']);
				chkAddWord($scam_comment, $last_descriptions, $dataU01['user_description']);
				break;
			}
		}
		foreach ( $listU01 as $chkU01 ) {
			if ( $chkU01['user_sex'] != $dataU01['user_sex'] ) {
				chkAddWord($scam_comment, "性別詐称疑い（メールアドレス）", $dataU01['user_description']);
				$diffsamemailinfo++;
			}
			if ( $chkU01['user_birthday'] != $dataU01['user_birthday'] ) {
				chkAddWord($scam_comment, "年齢詐称疑い（メールアドレス）", $dataU01['user_description']);
				$diffsamemailinfo++;
			}
			if ($diffsamemailinfo > 0) {
				break;
			}
		}
		if ($mailaddresscountforce > 0 ) {
			//過去強制退会あり
			$dataU01['forced_private'] = 3; //3：長期保留
			$dataU01['alert_flag'] = 1; //1：要注意
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			chkAddWord($scam_comment, "【強制退会】あり", $dataU01['user_description']);
			$cnt_red++;
		}
		if ($yellowmailaddresscount > 0 ) {
			//イエローカード
			$dataU01['alert_flag'] = 1; //1：要注意
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			chkAddWord($scam_comment, "【イエローカード】あり", $dataU01['user_description']);
			$cnt_yellow++;
		}
		if ($ipaddresscount > 0 ) {
			//IPアドレス
			$dataU01['alert_flag'] = 1; //1：要注意
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			chkAddWord($scam_comment, "【複数アカウント】疑い", $dataU01['user_description']);
			$cnt_ipaddress++;
		}
		if ($dataU01['enrollment_device_type'] == 3) {
			//PCは警戒が必要
			chkAddWord($scam_comment, "【PC】", $dataU01['user_description']);
		}
		if ($mailaddresscountforce == 0 && $yellowmailaddresscount == 0 && $ipaddresscount == 0) {
			if ($mailaddresscountmatch > 0 ) {
				chkAddWord($scam_comment, "出戻り", $dataU01['user_description']);
				if ($difflastuserinfo == 0 && $diffsamemailinfo == 0) {
					//過去の差異なし、かつ、過去マッチングあり（出戻り）
					$dataU01['forced_private'] = 0; //0：公開
					$dataU01['alert_flag'] = 0; //0：正常
					$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
					$cnt_normal++;
					$checkOcr = true;
				}
			}else if ($mailaddresscount > 0 ) {
				//過去マッチングなし（出戻り）
				chkAddWord($scam_comment, "前回マッチングなし", $dataU01['user_description']);
				$cnt_normal++;
			} else {
				// ----- メディア「不明」以外 -----
				$dataW07 = $dbW07->W07G03($dataU01['user_id']);
				if (empty($dataW07['M24_media'])) {
					$ret = getAccessMedia($dataU01['user_id']);
					if (!empty($ret['media']) || !empty($ret['organic'])) {
						$scam_comment .= '媒体:';
						if (!empty($ret['media'])) {
							// メディアあり
							$scam_comment .= $ret['media'].', ';
						}
						if (!empty($ret['organic'])) {
							// 自然検索
							$scam_value += 20;
							$scam_comment .= $ret['organic'].', ';
						}
					} else {
						// 経路なし
						$value = $dataU01['user_nickname'].$dataU01['one_word'].$dataU01['self_introduction'];
						$check_scam_comment = scamWordProfile($listbatchM30,$value,$dataU01['user_description']);
						if (!empty($check_scam_comment)) {
							$dataU01['alert_flag'] = 1; //1：要注意
							$scam_comment .= $check_scam_comment;
							$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
						}
						chkAddWord($scam_comment, "【経路不明】", $dataU01['user_description']);
					}
				}
			}
		}
		$value = $dataU01['user_nickname'].$dataU01['one_word'].$dataU01['self_introduction'];
		$check_scam_comment = scamWordProfile($listM30,$value,$dataU01['user_description']);
		if (!empty($check_scam_comment)) {
			//アラートワードあり
			$dataU01['forced_private'] = 2; //2：保留
			$dataU01['alert_flag'] = 1; //1：要注意
			$scam_comment .= $check_scam_comment;
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			$checkOcr = false;
		}
		if (!empty($scam_comment)) {
			if ( strpos($dataU01['user_description'], $scam_comment) === false ) {
				$dataU01['user_description'] .= $scam_comment; //備考
				$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			}
		}
		if ($checkOcr) {
			if(empty($dataU01['ocr_birthday']) && ($dataU01['identify_confirm'] == 2 )) {
				//申請中でOCR未チェックの場合のみ
				error_log(' ===== check ocr:'.$dataU01['ocr_birthday']);
				error_log(' ===== check ocr:'.$dataU01['identify_confirm']);
				error_log(' ===== check ocr:'.$dataU01['user_id']);
        $dataU03 = $dbU03->U03G01($dataU01['user_id'], 1);
        if (!empty($dataU03['user_id'])) {
					$file_path = $gIMAGE_PATH . 'user/' . sprintf('%08d', $dataU01['user_id']) . '/identification_' . sprintf('%08d',$dataU03['certificate_id']). '.' .$dataU03['image_extension'];
					$ocr_birthday = recognizeText($file_path);
					$dataU01['ocr_birthday'] = $ocr_birthday ? date('Ymd', strtotime($ocr_birthday)): $OCR_ERROR; //OCR読込不可の場合、99999999
					$cnt_ocr++;
					if ($dataU01['user_birthday'] == $dataU01['ocr_birthday']) {
						$dataU01['user_description'] .= '　OCR:OK'; //OK
						$dataU01['identify_confirm'] = 1; //1：承認
						$dataU01['identify_date_time'] = date('YmdHis');
						$dataU01['identify_administrator_id'] = $gSYSTEM_ADMIN_ID;
						$mail = new autoMail();
						$mail->approvalPermission($dataU01);
						$cnt_ocr_ok++;
						error_log('OCR OK:'.$dataU01['user_birthday'].' _ '.$dataU01['ocr_birthday']);
						// ----- 報酬計算 -----
						$reward_kind = 2; //2:承認
						error_log(basename(__FILE__).__LINE__.'| '.$reward_kind.' '. $dataU01['user_id'].' '. $gDOCUMENTROOT_PATH.' '. $gSYSTEM_ADMIN_ID);
						exec('php ' . $gDOCUMENTROOT_PATH.'async/addreward.php '.$reward_kind.' '.$dataU01['user_id'].' '.$gSYSTEM_ADMIN_ID.' 0'.' > /dev/null &');
						// ----- 身分証差し替え -----
						$oldname = $gIMAGE_PATH . 'user/' . sprintf('%08d',$dataU01['user_id']) . '/identification_' . sprintf('%08d',$dataU03['certificate_id']). '.' .$dataU03['image_extension'];
						$newname = $gIMAGE_PATH . 'user/' . sprintf('%08d',$dataU01['user_id']) . '/identification_00000001' . '.' .$dataU03['image_extension'];
						rename($oldname, $newname);
						$file_path = $gIMAGE_PATH . 'user/' . sprintf('%08d',$dataU01['user_id']) . '/identification_00000001';
						foreach(glob($file_path."*") as $file) {
							if ($newname != $file) {
								unlink($file);
							}
						}
						$file_path = $gIMAGE_PATH . 'user/' . sprintf('%08d',$dataU01['user_id']) . '/identification_00000003';
						foreach(glob($file_path."*") as $file) {
							unlink($file);
						}
						$dataU0301 = $dbU03->U03G01($dataU01['user_id'], 1);
						$dataU0301['image_extension'] = $dataU03['image_extension'];
						$dbU03->U03U01($dataU0301, basename(__FILE__, ".php"));
						// ----- 差し替え完了後のデータはRemove:1 -----
						$dbU03->U03R01($dataU01['user_id'], 3, basename(__FILE__, ".php"));
					} elseif ($dataU01['ocr_birthday'] == $OCR_ERROR) {
						$dataU01['user_description'] .= '　OCR:NG'; //NG
						error_log('OCR NG:'.$dataU01['user_birthday'].' _ '.$dataU01['ocr_birthday']);
					} else {
						$dataU01['user_description'] .= '　OCR:NG（不一致）'; //NG
						error_log('OCR NG:'.$dataU01['user_birthday'].' _ '.$dataU01['ocr_birthday']);
					}
					$dbU01->updateAllField($dataU01, basename(__FILE__, ".php"));
				}
			}
		}
	}
	error_log('cnt_red:'.$cnt_red.'/'.$total);
	error_log('cnt_yellow:'.$cnt_yellow.'/'.$total);
	error_log('cnt_ipaddress:'.$cnt_ipaddress.'/'.$total);
	error_log('cnt_normal:'.$cnt_normal.'/'.$total);
	error_log('cnt_media:'.$cnt_media.'/'.$total);
	error_log('cnt_ocr_ok:'.$cnt_ocr_ok.'/'.$cnt_ocr);

	// ====== 承認保留 ======
	$searchList = array();
	$searchList['un_scam'] = 3; //承認保留
	$searchList['user_status'] = 1; //1：稼働中
	$listU01 = $dbU01->U01S03($searchList);
	$addWord = "怪しい";
	$addScam = "詐欺疑い";
	foreach ( $listU01 as $dataU01 ) {
		if ( strpos($dataU01['user_description'], $addWord) === false ) {
			$dataU01['user_description'] = $addWord."　".$dataU01['user_description']; //備考
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
		}
		if ( $dataU01['alert_flag'] ==  1 ||  $dataU01['alert_flag'] ==  2) {
			//要注意・長期要注意
			if ( $dataU01['identify_confirm'] ==  2 && $dataU01['forced_private'] ==  0 ) {
				//承認保留・申請中の場合、公開したくない
				$dataU01['forced_private'] = 2; //2：保留
				$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			}
		}
		if ( $dataU01['forced_private'] ==  3 ) {
			//【長期保留】の場合、詐欺疑いとする
			if ( strpos($dataU01['user_description'], $addScam) === false ) {
				$dataU01['user_description'] .= '　'.$addScam; //備考
				$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			}
			//【長期保留】の場合に、自動で文言追加
			if ( strpos($dataU01['user_description'], $LONG_FORCE_PRIVATE) === false ) {
				$dataU01['user_description'] .= '　'.$LONG_FORCE_PRIVATE; //備考
				$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			}
		}
		if ($dataU01['forced_private'] !=  3 && strpos($dataU01['user_description'], $LONG_FORCE_PRIVATE) !== false) {
			//【長期保留】以外
			$dataU01['user_description'] = str_replace($LONG_FORCE_PRIVATE, '', $dataU01['user_description']);
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
		}
	}

	// ====== 要注意：男性（入金）  ======
	$searchList = array();
	$searchList['alert_flag'] = 9;
	$searchList['user_sex'] = 1; //1：男性
	$searchList['user_status'] = 1; //1：稼働中
	$searchList['include_scam_judge'] = 1;
	$listU01 = $dbU01->U01S03($searchList);
	$cnt_pay = 0;
	$total = count($listU01);
	foreach ( $listU01 as $dataU01 ) {
		$scam_comment='';
		$chkP01 = $dbP01->P01G17($dataU01['user_id'], 0, 1);
		if ($chkP01['payment_type'] == 2 || $chkP01['payment_type'] == 3 || $chkP01['payment_type'] == 4 || $chkP01['payment_type'] == 5 || $chkP01['payment_type'] == 6) {
			//銀行入金・AmazonPay・PayPay・LINEPay・Paidy
			$dataU01['alert_flag'] = 0; //0：正常
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			$cnt_pay++;
		}
	}
	error_log('cnt_pay:'.$cnt_pay.'/'.$total);

	// ====== 要注意チェック  ======
	$searchList = array();
	$searchList['alert_flag'] = 9;
	$searchList['user_status'] = 1; //1：稼働中
	$searchList['include_scam_judge'] = 1; //1：未判断を含む
	$listU01 = $dbU01->U01S03($searchList);
	$total = count($listU01);
	foreach ( $listU01 as $dataU01 ) {
		$scam_value = 0;
		$scam_comment='';
		// ====== 登録媒体 ======
		$dataW07 = $dbW07->W07G03($dataU01['user_id']);
		$dataW06 = $dbW06->W06G01($dataW07['access_media_id']);
		if (empty($dataW07['M24_media'])) {
			$ret = getAccessMedia($dataU01['user_id']);
			if (!empty($ret['media']) || !empty($ret['organic'])) {
				$scam_comment .= '媒体:';
				if (!empty($ret['media'])) {
					// メディアあり
					$scam_comment .= $ret['media'].', ';
				}
				if (!empty($ret['organic'])) {
					// 自然検索
					$scam_value += 20;
					$scam_comment .= $ret['organic'].', ';
				}
			} else {
				// 不明
				$scam_value += 30;
				$scam_comment .= '不明'.', ';
				if ($dataW06['referer'] == 'https://healmate.jp/') {
					$scam_value += 40;
				} else if ($dataW06['referer'] == 'https://healmate.jp/enrollment') {
					$scam_value += 50;
				} else if (	strpos($dataW06['referer'], 'https://healmate.jp/login') !== false ) {
					$scam_value += 50;
				} else if (empty($dataW06['referer'])) {
					$scam_value += 60;
				} else {
					$scam_value += 70;
				}
				if ($dataU01['user_sex'] == 1) {
					//男性の不明は優先してチェック
					$scam_value += 90;
				}
			}
		} else {
			if ($dataU01['user_sex'] == 2) {
				//女性
				if ($dataW06['landingpage_no'] == 301) {
					// https://hlmt.jp/
					$scam_value += 50;
					$scam_comment .= 'hlmt'.', ';
				}
			}
		}

		// ====== 登録日が直近 ======
		if ($dataU01['enrollment_date'] == date('Ymd')) {
			//当日
			$scam_value += 20;
			$scam_comment .= '本日'.', ';
		} else if ($dataU01['enrollment_date'] > date('Ymd', strtotime('-3 day'))) {
			//直近
			$scam_value += 15;
			$scam_comment .= '直近'.', ';
		}

		// ====== ログイン履歴 ======
		$cntU10 = $dbU10->U10C03($dataU01['user_id']);
		if ($cntU10 > 7) {
			$scam_value += 25;
			$scam_comment .= 'ログイン'.$cntU10.'ヶ所, ';
		} else if ($cntU10 > 5) {
			$scam_value += 20;
			$scam_comment .= 'ログイン'.$cntU10.'ヶ所, ';
		} else if ($cntU10 > 3) {
			$scam_value += 15;
			$scam_comment .= 'ログイン'.$cntU10.'ヶ所, ';
		} else if ($cntU10 > 1) {
			$scam_value += 10;
			$scam_comment .= 'ログイン'.$cntU10.'ヶ所, ';
		}

		// ====== ログイン履歴（デバイス） ======
		$cntU10 = $dbU10->U10C04($dataU01['user_id']);
		if ($cntU10 > 2) {
			$scam_value += 70;
			$scam_comment .= 'デバイス'.$cntU10.',';
		} else if ($cntU10 > 1) {
			$scam_value += 50;
			$scam_comment .= 'デバイス'.$cntU10.',';
		}

		// ====== 送ったいいねの都道府県が複数（全国） ======
		$cntU08 = $dbU08->U08C08($dataU01['user_id']);
		if ($cntU08 > 8) {
			$scam_value += 50;
			$scam_comment .= 'いいね県'.$cntU08.', ';
		} else if ($cntU08 > 6) {
			$scam_value += 35;
			$scam_comment .= 'いいね県'.$cntU08.', ';
		} else if ($cntU08 > 4) {
			$scam_value += 25;
			$scam_comment .= 'いいね県'.$cntU08.', ';
		} else if ($cntU08 > 2) {
			$scam_value += 15;
			$scam_comment .= 'いいね県'.$cntU08.', ';
		}

		// ====== もらったいいね <  送ったいいね ======
		$dataU42 = $dbU42->U42G01($dataU01['user_id']);
		$high_match = intval($dataU42['receive_like_count'] * 0.7);
		if ($dataU01['user_sex'] == 2) {
			//女性
			if ($dataU42['send_sorry_count'] == 0 && $dataU42['send_block_count'] == 0){
				if ($dataU42['receive_like_count'] < $dataU42['send_like_count']) {
					if (10 <= $dataU42['send_like_count']) {
						//いいね多い
						$scam_value += 25;
						$scam_comment .= 'い多'.', ';

						$diff = $dataU42['send_like_count'] - $dataU42['receive_like_count'];
						$scam_value += $diff;
					}
				}
				if ( $dataU42['receive_like_count'] > 10 && $dataU42['match_count'] >= 10) {
					if ( $high_match <= $dataU42['match_count']) {
						//マッチング多すぎ
						$scam_value += 30;
						$scam_comment .= 'マ多'.', ';
					}
				}
			}

			// ====== マッチング少ない ======
			if ( $dataU42['match_count'] <= 2) {
				if (10 <= $dataU42['send_message_count']) {
					$scam_value -= 15;
					$scam_comment .= 'マ少'.', ';
				}
			}
		}

		// ====== ごめんなさい ======
		if ($dataU42['send_sorry_count'] > 0){
			$scam_value += -15;
			$scam_comment .= 'ごめん'.', ';
		}
		// ====== ブロック ======
		if ($dataU42['send_block_count'] > 0){
			$scam_value += -10;
			$scam_comment .= 'ブロック'.', ';
		}

		// ====== プラン有効期限 ======
		if ($dataU01['user_sex'] == 1) {
			//男性
			if ($dataU01['plan_expiry_date'] > date('Ymd', strtotime('+2 month'))) {
				$scam_value += -33;
				$scam_comment .= 'プラン長期'.', ';
			}
		}

		// ====== PC ======
		if ($dataU01['enrollment_device_type'] == 3) {
			if ($dataU01['user_sex'] == 1) {
				//男性
				$scam_value += 10;
			} else {
				//女性
				$scam_value += 30;
			}
			$scam_comment .= 'PC'.', ';
		}

		// ====== 検索条件 ======
		$searchCond = getSerachCondition($dataU01['user_id']);
		if (empty($searchCond)) {
			$scam_value += 50;
			$scam_comment .= '検索条件なし'.', ';
		}


		// ====== 非公開モード ======
		if ($dataU01['none_public_status'] > 0) {
			$scam_value += -33;
			$scam_comment .= '非公開'.', ';
		}

		// ====== 警告あり ======
		if ($dataU01['warning_flag'] == 1) {
			$scam_value += 30;
			$scam_comment .= '警告'.', ';
		}

		// ====== 怪しい ======
		if ( strpos($dataU01['user_description'], '怪しい') !== false ) {
			$scam_value += 30;
			$scam_comment .= '怪しい'.', ';
		}

		// ====== 詐欺疑い ======
		if ( strpos($dataU01['user_description'], '詐欺疑い') !== false ) {
			$scam_value += 40;
			$scam_comment .= '詐欺疑い'.', ';
		}

		// ====== 監視 ======
		if ( strpos($dataU01['user_description'], '監視') !== false ) {
			$scam_value += 40;
			$scam_comment .= '監視'.', ';
		}

		// ====== 警戒 ======
		if ( strpos($dataU01['user_description'], '警戒') !== false ) {
			$scam_value += 40;
			$scam_comment .= '警戒'.', ';
		}

		// ====== キャリア携帯 ======
		$mobile_mail = ['ezweb.ne.jp','au.com','i.softbank.jp','softbank.ne.jp','docomo.ne.jp'];
		foreach ( $mobile_mail as $mail ) {
			if ( strpos($dataU01['user_mailaddress'], $mail) !== false ) {
				$scam_value += -5;
				$scam_comment .= 'キャリア'.', ';
			}
		}

		// ====== 年齢 ======
		$age = Age($dataU01['user_birthday']);
		if ($dataU01['user_sex'] == 2) {
			if ($age < 50 && $age >= 40) {
				$scam_value += 13;
				$scam_comment .= '40代'.', ';
			} else if ($age < 40 && $age >= 30) {
				$scam_value += 17;
				$scam_comment .= '30代'.', ';
			}
		}
		if ($dataU01['user_sex'] == 1) {
			if ($age < 50 && $age >= 40) {
				$scam_value += 17;
				$scam_comment .= '40代'.', ';
			} else if ($age < 40 && $age >= 30) {
				$scam_value += 13;
				$scam_comment .= '30代'.', ';
			}
		}

		if ($dataU42['send_message_count'] < 3) {
			$scam_judge = 1; //1:未判断
			$scam_comment = '【未判断】'.$scam_comment;
		} else {
			if ($scam_value > 60) $scam_judge = 4; //4:高
			if ($scam_value > 45 && $scam_value <= 60) $scam_judge = 3; //3:中
			if ($scam_value > 30 && $scam_value <= 45) $scam_judge = 2; //2:低
			if ($scam_value <= 40) {
				$scam_value = 0;
				$scam_judge = 0; //0:正常
				$dataU01['alert_flag'] = 0; //0：正常
				$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			}
		}
		$dataU51 = $dbU51->CreateNewDto();
		$dataU51['user_id'] = $dataU01['user_id'];
		$dataU51['scam_value'] = $scam_value;
		$dataU51['scam_judge'] = $scam_judge;
		$dataU51['scam_comment'] = $scam_comment;
		$dbU51->U51U01($dataU51, basename(__FILE__, ".php"));
	}

	error_log('batIdentifyConfirmAuto finish');
	exit();
