<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : U01_users
	class U01_users extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function U01G01($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_id='.$user_id);
			return $this->loadRecord($sel);
		}
		// ----- メールアドレス重複で1件 -----
		function U01G02($user_id, $user_mailaddress) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			if ( $user_id > 0 ) {
				$sel->addcond('user_id<>'.$user_id);
			}
			$sel->addcond('user_mailaddress="'.$user_mailaddress.'"');
			$sel->addcond('Remove=0');
			$sel->addorder('user_id DESC');
			return $this->loadRecord($sel);
		}
		// ----- コードで1件 -----
		function U01G03($user_code) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_code="'.$user_code.'"');
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- メールアドレス重複で1件（退会除） -----
		function U01G04($user_id, $user_mailaddress) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			if ( $user_id > 0 ) {
				$sel->addcond('user_id<>'.$user_id);
			}
			$sel->addcond('user_mailaddress="'.$user_mailaddress.'"');
			$sel->addcond('user_status=0');
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- plan_type -----
		function U01G05($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M20.plan_type');
			$sel->addcond('U01.user_id='.$user_id);
			$sel->addcond('U01.Remove=0');
			$sel->addinner('M20_plans M20', 'M20.plan_id=U01.plan_id');
			$sel->addcond('M20.Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- IPアドレス重複で1件 -----
		function U01G06($user_id, $Iuser) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			if ( $user_id > 0 ) {
				$sel->addcond('user_id<>'.$user_id);
			}
			$sel->addcond('Iuser="'.$Iuser.'"');
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- メールアドレス・会員ステータスで1件 -----
		function U01G07($user_mailaddress, $user_status) {
			$user_status = intval($user_status);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_mailaddress="'.$user_mailaddress.'"');
			if ( !empty($user_status) ) {
				$sel->addcond('user_status='.$user_status);
			}
			$sel->addcond('Remove=0');
			$sel->addorder('user_id DESC');
			return $this->loadRecord($sel);
		}
		// ----- PKで1件メイン画像 -----
		function U01G08($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('U01.user_id='.$user_id);
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addfield('U02.Udate as U02_Udate');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U01.user_id and U02.image_id = 1 and U02.Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- U59join -----
		function U01G09($user_id, $my_user_id) {
			$user_id = intval($user_id);
			$my_user_id = intval($my_user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U59.nickname');
			$sel->addcond('U01.user_id='.$user_id);
			$sel->addcond('U01.Remove=0');
			$sel->addleftjoin('U59_nicknames U59', 'U59.user_id='.$my_user_id.' and U59.nickname_user_id=U01.user_id and U59.Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- 全項目検索条件 -----
		function U01G10($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['previous_user_id']) ) {
				$sel->addcond('user_id<'.$searchList['previous_user_id']);
			}
			if ( !empty($searchList['user_mailaddress']) ) {
				$sel->addcond('user_mailaddress="'.$searchList['user_mailaddress'].'"');
			}
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['forced_private']) ) {
				if ( $searchList['forced_private'] == 99 ) $searchList['forced_private'] = 0;
				$sel->addcond('forced_private='.$searchList['forced_private']);
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) $searchList['user_status'] = 0;
				$sel->addcond('user_status='.$searchList['user_status']);
			}
			if ( !empty($searchList['user_status_except']) ) {
				$sel->addcond('user_status!='.$searchList['user_status_except']);
			}
			if ( !empty($searchList['enrollment_yearmonth']) ) {
				$sel->addcond('LEFT(enrollment_date,6)='.$searchList['enrollment_yearmonth']);
			}
			if ( !empty($searchList['enrollment_date']) ) {
				$sel->addcond('enrollment_date='.$searchList['enrollment_date']);
			}
			if ( !empty($searchList['previous_enrollment_date']) ) {
				$sel->addcond('enrollment_date<'.$searchList['previous_enrollment_date']);
			}
			if ( !empty($searchList['withdrawal_yearmonth']) ) {
				$sel->addcond('LEFT(withdrawal_date,6)='.$searchList['withdrawal_yearmonth']);
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) $searchList['user_status'] = 0;
				$sel->addcond('user_status='.$searchList['user_status']);
			}
			if ( !empty($searchList['exclusion_user_status']) ) {
				if ( $searchList['exclusion_user_status'] == 99 ) $searchList['exclusion_user_status'] = 0;
				$sel->addcond('user_status<>'.$searchList['exclusion_user_status']);
			}
			if ( !empty($searchList['dummy_flag']) ) {
				if ( $searchList['dummy_flag'] == 99 ) $searchList['dummy_flag'] = 0;
				$sel->addcond('forced_private='.$searchList['dummy_flag']);
			}
			if ( !empty($searchList['last_action_date_time_start']) ) {
				$sel->addcond('last_action_date_time>='.$searchList['last_action_date_time_start']);
			}
			if ( !empty($searchList['last_action_date_time_finish']) ) {
				$sel->addcond('last_action_date_time<='.$searchList['last_action_date_time_finish']);
			}
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function U01S01() {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		function U01S02($selfU01, $searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U02M.image_id');
			$sel->addfield('U02M.image_code');
			$sel->addfield('U02M.image_extension');
			$sel->addfield('U02M.is_unpublish');
			$sel->addfield('U02M.publish_version_id');
			$sel->addfield('U02M.thumbnail_version_id');
			$sel->addfield('U02M.Udate as U02_Udate');
			$sel->addfield('U04.deny_flag');
			$sel->addfield('U36.flag_value');
			$sel->addfield('U59.nickname');
			$sel->addfield('M20.plan_type');
			if ( !empty($searchList['search_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['search_sex']);
			}
			if ( $searchList['search_type'] == 1 ) {
				$sel->addorder('U01.last_action_date_time DESC');
				if ( !empty($searchList['search_datetime']) ) {
					$sel->addcond('U01.last_action_date_time<='.$searchList['search_datetime']);
				}
			} else if ( $searchList['search_type'] == 2 ) {
				$sel->addorder('U01.user_id DESC');
			} else if ( $searchList['search_type'] == 3 ) {
				$sel->addorder('U01.last_profile_date_time DESC');
			}
			if ( !empty($searchList['search_spot_nickname']) ) {
				$sel->addcond('(U01.user_nickname like "%'.$searchList['search_spot_nickname'].'%")');
			}
			if ( !empty($searchList['search_spot_keyword']) ) {
				$sel->addcond('(U01.one_word like "%'.$searchList['search_spot_keyword'].'%" or U01.self_introduction like "%'.$searchList['search_spot_keyword'].'%")');
			}
			if ( !empty($searchList['search_spot_date_fee']) ) {
				$sel->addcond('(U01.about_date_fee like "%'.$searchList['search_spot_date_fee'].'%")');
			}
			if ( !empty($searchList['search_spot_desired_age']) ) {
				$sel->addcond('(U01.desired_age like "%'.$searchList['search_spot_desired_age'].'%")');
			}
			if ( !empty($searchList['search_age_start']) ) {
				$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) >=' . $searchList['search_age_start']);
			}
			if ( !empty($searchList['search_age_finish']) ) {
				$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) <=' . $searchList['search_age_finish']);
			}
			if ( !empty($searchList['search_prefecture_code']) ) {
				$sel->addcond('U01.redidence_prefecture_code in (' . $searchList['search_prefecture_code'] . ')');
			}
			if ( !empty($searchList['search_birth_prefecture_code']) ) {
				$sel->addcond('U01.birthplace_prefecture_code in (' . $searchList['search_birth_prefecture_code'] . ')');
			}
			if ( !empty($searchList['search_school_career_id']) ) {
				$sel->addcond('U01.school_career_id in (' . $searchList['search_school_career_id'] . ')');
			}
			if ( !empty($searchList['search_job_id']) ) {
				if ( strpos($searchList['search_job_id'], '999999999') !== false ) {
					$array_job_id = explode(',', $searchList['search_job_id']);
					$condstr = '';
					foreach ( $array_job_id as $job_id ) {
						if ( $job_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U25.job_id='.$job_id.' and U25.Remove=0)';
						}
					}
					$sel->addleftjoin('U25_jobs U25', 'U25.user_id=U01.user_id and U25.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U25.user_id is NULL');
					} else {
						$sel->addcond('(U25.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U25_jobs where job_id in (' . $searchList['search_job_id'] . ') and Remove = 0)');
				}
			}
			if ( $searchList['search_income_id'] != "" ) {
				$sel->addcond('U01.income_id in (' . $searchList['search_income_id'] . ')');
			}
			if ( !empty($searchList['search_character_id']) ) {
				if ( strpos($searchList['search_character_id'], '999999999') !== false ) {
					$array_character_id = explode(',', $searchList['search_character_id']);
					$condstr = '';
					foreach ( $array_character_id as $character_id ) {
						if ( $character_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U12.character_id='.$character_id.' and U12.Remove=0)';
						}
					}
					$sel->addleftjoin('U12_characters U12', 'U12.user_id=U01.user_id and U12.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U12.user_id is NULL');
					} else {
						$sel->addcond('(U12.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U12_characters where character_id in (' . $searchList['search_character_id'] . ') and Remove = 0)');
				}
			}
			if ( $searchList['search_personality_id'] != "" ) {
				$sel->addcond('U01.personality_id in (' . $searchList['search_personality_id'] . ')');
			}
			if ( !empty($searchList['search_no_entry_height']) ) {
				if ( !empty($searchList['search_height_start']) ) {
					$sel->addcond('(U01.user_height >=' . $searchList['search_height_start'] . ' or U01.user_height = 0)');
				}
				if ( !empty($searchList['search_height_finish']) ) {
					$sel->addcond('(U01.user_height <=' . $searchList['search_height_finish'] . ' or U01.user_height = 0)');
				}
				if ( empty($searchList['search_height_start']) && empty($searchList['search_height_finish']) ) {
					$sel->addcond('U01.user_height = 0');
				}
			} else {
				if ( !empty($searchList['search_height_start']) ) {
					$sel->addcond('U01.user_height >=' . $searchList['search_height_start']);
				}
				if ( !empty($searchList['search_height_finish']) ) {
					$sel->addcond('U01.user_height <=' . $searchList['search_height_finish']);
				}
			}
			if ( !empty($searchList['search_body_id']) ) {
				if ( strpos($searchList['search_body_id'], '999999999') !== false ) {
					$array_body_id = explode(',', $searchList['search_body_id']);
					$condstr = '';
					foreach ( $array_body_id as $body_id ) {
						if ( $body_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U13.body_id='.$body_id.' and U13.Remove=0)';
						}
					}
					$sel->addleftjoin('U13_bodys U13', 'U13.user_id=U01.user_id and U13.sex_id= '.$searchList['search_sex'].' and U13.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U13.user_id is NULL');
					} else {
						$sel->addcond('(U13.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U13_bodys where body_id in (' . $searchList['search_body_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( $searchList['search_blood'] != "" ) {
				$sel->addcond('U01.user_blood in (' . $searchList['search_blood'] . ')');
			}
			if ( $searchList['search_alcohol_id'] != "" ) {
				$sel->addcond('U01.alcohol_id in (' . $searchList['search_alcohol_id'] . ')');
			}
			if ( $searchList['search_cigarette_id'] != "" ) {
				$sel->addcond('U01.cigarette_id in (' . $searchList['search_cigarette_id'] . ')');
			}
			if ( $searchList['search_housemate_id'] != "" ) {
				$sel->addcond('U01.housemate_id in (' . $searchList['search_housemate_id'] . ')');
			}
			if ( $searchList['search_family_relationship_id'] != "" ) {
				$sel->addcond('U01.family_relationship_id in (' . $searchList['search_family_relationship_id'] . ')');
			}
			if ( $searchList['search_family_continuation_relationship_id'] != "" ) {
				$sel->addcond('U01.family_continuation_relationship_id in (' . $searchList['search_family_continuation_relationship_id'] . ')');
			}
			if ( $searchList['search_child_id'] != "" ) {
				$sel->addcond('U01.child_id in (' . $searchList['search_child_id'] . ')');
			}
			if ( !empty($searchList['search_hobby_id']) ) {
				if ( strpos($searchList['search_hobby_id'], '999999999') !== false ) {
					$array_hobby_id = explode(',', $searchList['search_hobby_id']);
					$condstr = '';
					foreach ( $array_hobby_id as $hobby_id ) {
						if ( $hobby_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U14.hobby_id='.$hobby_id.' and U14.Remove=0)';
						}
					}
					$sel->addleftjoin('U14_hobbies U14', 'U14.user_id=U01.user_id and U14.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U14.user_id is NULL');
					} else {
						$sel->addcond('(U14.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U14_hobbies where hobby_id in (' . $searchList['search_hobby_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_relation_id']) ) {
				if ( strpos($searchList['search_favorite_relation_id'], '999999999') !== false ) {
					$array_favorite_relation_id = explode(',', $searchList['search_favorite_relation_id']);
					$condstr = '';
					foreach ( $array_favorite_relation_id as $favorite_relation_id ) {
						if ( $favorite_relation_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U18.favorite_relation_id='.$favorite_relation_id.' and U18.Remove=0)';
						}
					}
					$sel->addleftjoin('U18_favorite_relations U18', 'U18.user_id=U01.user_id and U18.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U18.user_id is NULL');
					} else {
						$sel->addcond('(U18.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U18_favorite_relations where favorite_relation_id in (' . $searchList['search_favorite_relation_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_time_zone_id']) ) {
				if ( strpos($searchList['search_favorite_time_zone_id'], '999999999') !== false ) {
					$array_favorite_time_zone_id = explode(',', $searchList['search_favorite_time_zone_id']);
					$condstr = '';
					foreach ( $array_favorite_time_zone_id as $favorite_time_zone_id ) {
						if ( $favorite_time_zone_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U19.favorite_time_zone_id='.$favorite_time_zone_id.' and U19.Remove=0)';
						}
					}
					$sel->addleftjoin('U19_favorite_time_zones U19', 'U19.user_id=U01.user_id and U19.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U19.user_id is NULL');
					} else {
						$sel->addcond('(U19.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U19_favorite_time_zones where favorite_time_zone_id in (' . $searchList['search_favorite_time_zone_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_meet_process_id']) ) {
				if ( strpos($searchList['search_meet_process_id'], '999999999') !== false ) {
					$array_meet_process_id = explode(',', $searchList['search_meet_process_id']);
					$condstr = '';
					foreach ( $array_meet_process_id as $meet_process_id ) {
						if ( $meet_process_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U39.meet_process_id='.$meet_process_id.' and U39.Remove=0)';
						}
					}
					$sel->addleftjoin('U39_meet_processes U39', 'U39.user_id=U01.user_id and U39.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U39.user_id is NULL');
					} else {
						$sel->addcond('(U39.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U39_meet_processes where meet_process_id in (' . $searchList['search_meet_process_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_visual_id']) ) {
				if ( strpos($searchList['search_favorite_visual_id'], '999999999') !== false ) {
					$array_favorite_visual_id = explode(',', $searchList['search_favorite_visual_id']);
					$condstr = '';
					foreach ( $array_favorite_visual_id as $favorite_visual_id ) {
						if ( $favorite_visual_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U15.favorite_visual_id='.$favorite_visual_id.' and U15.Remove=0)';
						}
					}
					$sel->addleftjoin('U15_favorite_visuals U15', 'U15.user_id=U01.user_id and U15.sex_id='.$searchList['search_sex'].' and U15.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U15.user_id is NULL');
					} else {
						$sel->addcond('(U15.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U15_favorite_visuals where favorite_visual_id in (' . $searchList['search_favorite_visual_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_character_id']) ) {
				if ( strpos($searchList['search_favorite_character_id'], '999999999') !== false ) {
					$array_favorite_character_id = explode(',', $searchList['search_favorite_character_id']);
					$condstr = '';
					foreach ( $array_favorite_character_id as $favorite_character_id ) {
						if ( $favorite_character_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U17.favorite_character_id='.$favorite_character_id.' and U17.Remove=0)';
						}
					}
					$sel->addleftjoin('U17_favorite_characters U17', 'U17.user_id=U01.user_id and U17.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U17.user_id is NULL');
					} else {
						$sel->addcond('(U17.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U17_favorite_characters where favorite_character_id in (' . $searchList['search_favorite_character_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_body_id']) ) {
				if ( strpos($searchList['search_favorite_body_id'], '999999999') !== false ) {
					$array_favorite_body_id = explode(',', $searchList['search_favorite_body_id']);
					$condstr = '';
					foreach ( $array_favorite_body_id as $favorite_body_id ) {
						if ( $favorite_body_id != 999999999 ) {
							if ( !empty($condstr) ) {
								$condstr .= ' or ';
							}
							$condstr .= '(U16.favorite_body_id='.$favorite_body_id.' and U16.Remove=0)';
						}
					}
					$sel->addleftjoin('U16_favorite_bodys U16', 'U16.user_id=U01.user_id and U16.sex_id='.$searchList['search_sex'].' and U16.Remove=0');
					if ( empty($condstr) ) {
						$sel->addcond('U16.user_id is NULL');
					} else {
						$sel->addcond('(U16.user_id is NULL or ' . $condstr . ')');
					}
				} else {
					$sel->addcond('U01.user_id in (select user_id from U16_favorite_bodys where favorite_body_id in (' . $searchList['search_favorite_body_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_omit_like']) ) {
				$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $selfU01['user_id'] . ') U08','U08.user_id=U01.user_id');
				$sel->addcond('U08.user_id IS NULL');
			}
			if ( !empty($searchList['search_omit_give_like']) ) {
				$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $selfU01['user_id'] . ') U0802','U0802.like_user_id=U01.user_id');
				$sel->addcond('U0802.like_user_id IS NULL');
			}
			if ( !empty($searchList['search_omit_match']) ) {
				$sel->addleftjoin('(select match_user_id from U09_matches where user_id = ' . $selfU01['user_id'] . ') U09','U09.match_user_id=U01.user_id');
				$sel->addcond('U09.match_user_id IS NULL');
			}
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02M', 'U02M.user_id=U01.user_id and U02M.image_id = 1 and U02M.Remove=0');
			if ( !empty($searchList['my_user_id']) ) {
				$sel->addleftjoin('U59_nicknames U59', 'U59.user_id='.$searchList['my_user_id'].' and U59.nickname_user_id = U01.user_id and U59.Remove=0');
			}
			// ----- オンライン表示 -----
			$sel->addleftjoin('U04_mail U04', 'U04.user_id=U01.user_id and U04.mail_id = 12 and U04.Remove=0');
			// ----- PREMIUM表示 -----
			$sel->addleftjoin('U36_flags U36', 'U36.user_id=U01.user_id and U36.flag_id = 6 and U36.Remove=0');
			// ----- ごめんなさい除外 -----
			$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $selfU01['user_id'] . ' and is_no_like = 1) U0803','U0803.like_user_id=U01.user_id');
			$sel->addcond('U0803.like_user_id IS NULL');
			$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $selfU01['user_id'] . ' and is_no_like = 1) U0804','U0804.user_id=U01.user_id');
			$sel->addcond('U0804.user_id IS NULL');
			// ----- マッチングした相手除外（お蔵入り） -----
//			$sel->addleftjoin('(select match_user_id from U09_matches U09IN01 inner join U01_users U01IN01 on (U01IN01.user_id = U09IN01.match_user_id and U01IN01.is_match_hide = 1) where U09IN01.user_id = ' . $selfU01['user_id'] . ') U0901','U0901.match_user_id=U01.user_id');
//			$sel->addcond('U0901.match_user_id IS NULL');
			// ----- いいねした相手除外（お蔵入り） -----
//			$sel->addleftjoin('(select like_user_id from U08_likes U08IN01 inner join U01_users U01IN01 on (U01IN01.user_id = U08IN01.like_user_id and U01IN01.is_like_hide = 1) where U08IN01.user_id = ' . $selfU01['user_id'] . ') U0801','U0801.like_user_id=U01.user_id');
//			$sel->addcond('U0801.like_user_id IS NULL');
			if ( !empty($searchList['search_identify_confirm']) ) {
				$sel->addcond('U01.identify_confirm = 1');
			}
			if ( !empty($searchList['search_is_main_image']) ) {
				$sel->addinner('U02_user_images U02','U02.user_id=U01.user_id');
				$sel->addcond('U02.image_type=1');
				$sel->addcond('U02.image_extension!=""');
				$sel->addcond('U02.is_unpublish=0');
				$sel->addcond('U02.Remove=0');
			}
			$sel->addcond('U01.none_public_status=0');
			if ( empty($selfU01['forced_private']) ) {
				$sel->addcond('U01.forced_private=0');	// フラグの持ち方要注意 0だったら入る
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addgroup('U01.user_id');
			$sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			if ( !empty($searchList['search_limit']) ) {
				$sel->setoffset($searchList['search_offset']);
				$sel->setlimit($searchList['search_limit']);
			}
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $selfU01['user_id'] . ') U24','U24.block_user_id=U01.user_id');
			$sel->addcond('U24.block_user_id IS NULL');
//			$sel->addcond('NOT EXISTS ( SELECT U24.block_user_id FROM U24_block_targets U24 WHERE U24.user_id=' . $selfU01['user_id'] . ' and U24.block_user_id=U01.user_id LIMIT 1 )');
//print($sel->getsql());
//error_log($sel->getsql());
			return $this->loadList($sel);
		}
		function U01S03($searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('MAX(U10.Idate) as U10_Idate');
			$sel->addfield('M20.plan_type');
			$sel->addfield('M20.plan_title');
			$sel->addfield('M20.term_type');
			$sel->addfield('M20.plan_term');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			if ( !empty($searchList['except_self']) && !empty($searchList['user_id']) ) {
				$sel->addcond('U01.user_id!='.$searchList['user_id']);
			}
			if ( !empty($searchList['keyword']) ) {
				$sel->addcond('(U01.user_code="'.$searchList['keyword'].'" or U01.user_mailaddress like "%' .$searchList['keyword'].'%" or U01.user_nickname like "'.$searchList['keyword'].'%")');
			}
			if ( !empty($searchList['user_mailaddress']) ) {
				$sel->addcond('U01.user_mailaddress="'.$searchList['user_mailaddress'].'"');
			}
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['user_age_start']) ) {
				$sel->addcond('U01.user_birthday<='.(date('Ymd') - $searchList['user_age_start'] * 10000));
			}
			if ( !empty($searchList['user_age_finish']) ) {
				$sel->addcond('U01.user_birthday>='.(date('Ymd') - ($searchList['user_age_finish'] + 1) * 10000));
			}
			if ( !empty($searchList['user_birthday']) ) {
				$searchList['user_birthday'] = date('Ymd', strtotime($searchList['user_birthday']));
				$sel->addcond('U01.user_birthday='.$searchList['user_birthday']);
			}
			if ( !empty($searchList['redidence_prefecture_code']) ) {
				if ( $searchList['redidence_prefecture_code'] == 99 ) {
					$sel->addcond('U01.redidence_prefecture_code=0');
				} else {
					$sel->addcond('U01.redidence_prefecture_code='.$searchList['redidence_prefecture_code']);
				}
			}
			if ( !empty($searchList['plan_type']) ) {
				$sel->addcond('M20.plan_type='.$searchList['plan_type']);
			}
			if ( !empty($searchList['identify_confirm']) ) {
				if ( $searchList['identify_confirm'] == 99 ) $searchList['identify_confirm'] = 0;
				$sel->addcond('identify_confirm='.$searchList['identify_confirm']);
			}
			if ( !empty($searchList['is_identify_check']) ) {
				$sel->addcond('((U01.forced_private = 2 and U01.identify_confirm = 0) or (U01.forced_private in (0,2) and U01.identify_confirm = 2))');
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) {
					$sel->addcond('U01.user_status>0');
				} else if ( $searchList['user_status'] == 98 ) {
					$sel->addcond('U01.user_status!=10');
				} else {
					if ( $searchList['user_status'] == 1 ) $searchList['user_status'] = 0;
					$sel->addcond('U01.user_status='.$searchList['user_status']);
				}
			}
			if ( !empty($searchList['private']) ) {
				if ( $searchList['private'] == 1 ) {
					$sel->addcond('U01.none_public_status=1');
				} else if ( $searchList['private'] == 11 ) {
					$sel->addcond('U01.forced_private=1');
				} else if ( $searchList['private'] == 12 ) {
					$sel->addcond('U01.forced_private=2');
				} else if ( $searchList['private'] == 13 ) {
					$sel->addcond('U01.forced_private=3');
				} else if ( $searchList['private'] == 21 ) {
					$sel->addcond('U01.forced_private!=1');
				} else if ( $searchList['private'] == 22 ) {
					$sel->addcond('U01.none_public_status=0');
					$sel->addcond('U01.forced_private=0');
				} else if ( $searchList['private'] == 23 ) {
					$sel->addcond('U01.forced_private=0');
				}
			}
			if ( !empty($searchList['enrollment_date_start']) ) {
				$enrollment_date_start = date('Ymd', strtotime($searchList['enrollment_date_start']));
				$sel->addcond('U01.enrollment_date>='.$enrollment_date_start);
			}
			if ( !empty($searchList['enrollment_date_finish']) ) {
				$enrollment_date_finish = date('Ymd', strtotime($searchList['enrollment_date_finish']));
				$sel->addcond('U01.enrollment_date<='.$enrollment_date_finish);
			}
			if ( !empty($searchList['dummy_flag']) ) {
				if ( $searchList['dummy_flag'] == 9 ) {
					$sel->addcond('U01.dummy_flag=0');
				} else if ( !empty($searchList['dummy_flag']) ) {
					$sel->addcond('U01.dummy_flag='.$searchList['dummy_flag']);
				}
			}
			if ( !empty($searchList['alert_flag']) ) {
				$alert_flag = $searchList['alert_flag'];
				if ( $searchList['alert_flag'] == 9 ) {
					$alert_flag = 1;
					$sel->addcond('last_talk_date_time>0');
					$sel->addcond('last_talk_date_time IS NOT NULL');
				}
				$sel->addcond('U01.alert_flag='.$alert_flag);
			}
			if ( !empty($searchList['is_alert_user']) ) {
				$sel->addcond('U01.alert_flag>0');
			}
			if ( !empty($searchList['warning_flag']) ) {
				$sel->addcond('U01.warning_flag='.$searchList['warning_flag']);
			}
			if ( !empty($searchList['scam']) ) {
				if ( $searchList['scam'] == 1 ) {
					$sel->addcond('U01.un_scam=0');
					$sel->addinner('W17_scam_lists W17', '(W17.scam_mailaddress=U01.user_mailaddress or W17.scam_ipaddress=U01.Iuser)');
					$sel->addcond('W17.Remove=0');
				} else if ( $searchList['scam'] == 2 ) {
					$sel->addcond('U01.one_word="初めまして,よろしくお願いします"');
					$sel->addcond('U01.user_nickname REGEXP "^[a-zA-Z]+$"');
					$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
					$sel->addcond('W07.media=0');
					$sel->addcond('W07.Remove=0');
				}
			}
			if ( !empty($searchList['un_scam']) ) {
				if ( $searchList['un_scam'] == 99 ) $searchList['un_scam'] = 0;
				$sel->addcond('U01.un_scam='.$searchList['un_scam']);
			}
			if ( !empty($searchList['user_nickname']) ) {
				$sel->addcond('(U01.user_nickname like "'.$searchList['user_nickname'].'%"');
			}
			if ( !empty($searchList['media']) ) {
				$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
				if ( $searchList['media'] == 999999999 ) {
					$sel->addcond('W07.media=0');
				} else {
					$sel->addcond('W07.media='.$searchList['media']);
				}
				$sel->addcond('W07.Remove=0');
			}
			if ( !empty($searchList['ipaddress']) ) {
				$sel->addcond('U01.Iuser="'.$searchList['ipaddress'].'"');
			}
			if ( !empty($searchList['profile']) ) {
				//キーワード検索（スペースで分割して、and検索）
				$condkeyword = seach_split("concat(U01.one_word, U01.self_introduction, U01.user_description)", $searchList['profile'], 'and');
				$sel->addcond($condkeyword);
			}
			if ( !empty($searchList['order_id']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.order_id='.intval($searchList['order_id']));
				$sel->addcond('P01.Remove=0');
			}
			if ( !empty($searchList['payment']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.price>0');
				$sel->addcond('P01.payment_result=1');
				$sel->addcond('P01.tran_date_time>='.date('YmdHis', mktime(0, 0, 0, date('m') - 1, date('d'), date('Y'))));
				$sel->addcond('P01.Remove=0');
			}
			if ( !empty($searchList['yellow_card']) ) {
				$sel->addinner('U45_yellow_cards U45', 'U45.user_id=U01.user_id');
				$sel->addcond('U45.Remove=0');
			}
			if ( !empty($searchList['alert_flag']) && $searchList['alert_flag'] == 9 ) {
				//詐欺指数
				$sel->addfield('U51.scam_value');
				$sel->addfield('U51.scam_judge');
				$sel->addfield('U51.scam_comment');
				if (empty($searchList['include_scam_judge'])) $sel->addcond('U51.scam_judge != 1'); //「1:未判断」を除く
				$sel->addleftjoin('U51_scam_index U51', 'U51.user_id=U01.user_id');
			}
			if ( !empty($searchList['phone']) ) {
				$sel->addinner('U49_two_factors U49', 'U49.user_id=U01.user_id');
				$sel->addcond('U49.phone_number="'.preg_replace("/[^0-9]/", "", $searchList['phone']).'"');
				$sel->addcond('U49.is_confirm=1');
				$sel->addcond('U49.Remove=0');
			}
			$sel->addcond('U01.Remove=0');
			$sel->addgroup('U01.user_id');
			$sel->addleftjoin('U10_login_histories U10','U10.user_id=U01.user_id');
			$sel->addleftjoin('M20_plans M20','M20.plan_id=U01.plan_id and M20.Remove=0');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U01.user_id and U02.image_id = 1 and U02.Remove=0 and U02.is_unpublish=0'); //公開中
			if ( !empty($searchList['monitor']) ) {
				$sel->addinner('A14_monitoring_users A14', 'A14.user_id=U01.user_id');
				$sel->addcond('A14.administrator_id='.$_SESSION['administrator_id']);
				$sel->addcond('A14.monitoring_status='.$searchList['monitor']);
				$sel->addcond('A14.Remove=0');
			}
			if ( !empty($searchList['limit']) ) {
				$sel->setoffset(($searchList['page'] - 1) * $searchList['limit']);
				$sel->setlimit($searchList['limit']);
			}
			if ( !empty($searchList['sort']) && $searchList['sort'] == 1 ) {
				$sel->addorder('U01.user_id');
			} else if ( !empty($searchList['user_status']) ) {
				$sel->addorder('U01.withdrawal_date DESC');
			} else if ( !empty($searchList['alert_flag']) && $searchList['alert_flag'] == 9 ) {
				if ($searchList['sort']==11) {
					$sel->addorder('U51.scam_judge DESC, U51.scam_value DESC, U01.last_talk_date_time DESC');
				} else if ($searchList['sort']==12) {
					$sel->addorder('U51.scam_judge, U51.scam_value, U01.last_talk_date_time DESC');
				} else {
					$sel->addorder('U01.last_talk_date_time DESC');
				}
			} else if ( !empty($searchList['identify_confirm']) && !empty($searchList['private']) && $searchList['private'] == 13 ) {
				$sel->addorder('U01.Udate DESC');
			} else if ( !empty($searchList['identify_confirm']) ) {
				$sel->addorder('U01.identify_date_time');
			} else if ( !empty($searchList['private']) && $searchList['private'] == 12 ) {
				$sel->addorder('U01.user_id');
			} else {
				$sel->addorder('U01.user_id DESC');
			}
//print($sel->getsql());
			return $this->loadList($sel);
		}
		// ----- ダミー、公開者 -----
		function U01S04($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['user_birthday_start']) ) {
				$sel->addcond('user_birthday>=' . $searchList['user_birthday_start']);
			}
			if ( !empty($searchList['user_birthday_finish']) ) {
				$sel->addcond('user_birthday<=' . $searchList['user_birthday_finish']);
			}
			$sel->addcond('none_public_status=0');
			$sel->addcond('forced_private=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=1');
			$sel->addcond('Remove=0');
			if ( !empty($searchList['user_birthday_start']) ) {
				$sel->addorder('user_birthday');
			}
			return $this->loadList($sel);
		}
		// ----- 男性会員自動いいね候補 -----
		function U01S05() {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_sex=1');
			$sel->addcond('plan_id=0');
			$sel->addcond('none_public_status=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Idate<"'.date('Y-m-d H:i:s', mktime(date('H') - 1, date('i') - 30, date('s'), date('m'), date('d'), date('Y'))).'"');
			$sel->addcond('Idate>="'.date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))).'"');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- プラン有効期限切れリスト -----
		function U01S06($date) {
			$date = intval($date);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('plan_id>0');
			$sel->addcond('plan_expiry_date<'.$date);
			$sel->addcond('plan_expiry_date>0');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- カード課金対象リスト -----
		function U01S07($date, $payment_agency) {
			$date = intval($date);
			$payment_agency = intval($payment_agency);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M20.plan_title');
			$sel->addfield('M20.term_type');
			$sel->addfield('M20.plan_term');
			$sel->addfield('M20.plan_price');
			$sel->addfield('M20.renewal_plan_id');
			$sel->addcond('U01.plan_id>0');
			$sel->addcond('U01.plan_expiry_date='.$date);
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addinner('P01_payments P01','P01.user_id=U01.user_id');
			$sel->addcond('P01.payment_type=1');
			$sel->addcond('P01.payment_agency='.$payment_agency);
			$sel->addcond('P01.sales_item=0');
			$sel->addcond('P01.payment_result=1');
			$sel->addcond('P01.Remove=0');
			$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
			$sel->addcond('M20.Remove=0');
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// ----- 銀行振込対象リスト -----
		function U01S08($date) {
			$date = intval($date);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M20.plan_title');
			$sel->addfield('M20.term_type');
			$sel->addfield('M20.plan_term');
			$sel->addfield('M20.plan_price');
			$sel->addfield('M20.renewal_plan_id');
			$sel->addcond('U01.plan_id>0');
			$sel->addcond('U01.plan_expiry_date='.$date);
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addinner('P01_payments P01','P01.user_id=U01.user_id');
			$sel->addcond('(P01.payment_type=2 or P01.payment_type=4 or P01.payment_type=5)');
			$sel->addcond('P01.sales_item=0');
			$sel->addcond('P01.payment_result=1');
			$sel->addcond('P01.Remove=0');
			$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
			$sel->addcond('M20.Remove=0');
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// ----- 退会者リスト -----
		function U01S09($date) {
			$date = intval($date);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('W07.media');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addcond('U01.user_status=9');
			$sel->addcond('U01.withdrawal_date='.$date);
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addleftjoin('W07_browse_media W07','W07.user_id=U01.user_id');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U01.user_id and U02.image_id = 1 and U02.Remove=0 and U02.is_unpublish=0'); //公開中
			$sel->addcond('W07.Remove=0');
			return $this->loadList($sel);
		}
		// ----- 女性会員自動いいね候補 -----
		function U01S10() {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_sex=2');
			$sel->addcond('none_public_status=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Idate<"'.date('Y-m-d H:i:s', mktime(date('H') - 1, date('i') - 30, date('s'), date('m'), date('d'), date('Y'))).'"');
			$sel->addcond('Idate>="'.date('Y-m-d H:i:s', mktime(0, 0, 0, date('m'), date('d') - 1, date('Y'))).'"');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- 割引クーポン対象リスト -----
		function U01S11($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('user_sex='.$searchList['user_sex']);
			}
			$sel->addcond('plan_id=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=0');
			if ( !empty($searchList['enrollment_date']) ) {
				$sel->addcond('enrollment_date='.$searchList['enrollment_date']);
			}
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- LTV会員情報ダウンロード -----
		function U01S12($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) && $searchList['user_sex'] < 3 ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			$sel->addcond('U01.forced_private=0');
//			if ( !empty($searchList['user_status']) ) {
//				$sel->addcond('U01.user_status='.$searchList['user_status']);
//			}
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			if ( !empty($searchList['enrollment_yearmonth']) ) {
				$sel->addcond('LEFT(U01.enrollment_date,6)='.$searchList['enrollment_yearmonth']);
			}
			if ( !empty($searchList['identify_confirm']) ) {
				$sel->addcond('U01.identify_confirm='.$searchList['identify_confirm']);
			}
			if ( !empty($searchList['plan_expiry_date']) ) {
				$sel->addcond('U01.plan_expiry_date>0');
			}
			$sel->addcond('U01.Remove=0');
			$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
			if ( !empty($searchList['media']) ) {
				if ( $searchList['media'] == 999999999 ) {
					$sel->addcond('W07.media=0');
				} else {
					$sel->addcond('W07.media='.$searchList['media']);
				}
			}
			$sel->addcond('W07.Remove=0');
			if ( !empty($searchList['payment']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.payment_result=1');
				$sel->addcond('P01.Remove=0');
			}
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// ----- 地域別リスト -----
		function U01S13($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['redidence_prefecture_code']) ) {
				$sel->addcond('U01.redidence_prefecture_code in ('.$searchList['redidence_prefecture_code'].')');
			}
			$sel->addcond('U01.none_public_status=0');
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			if ( !empty($searchList['plan_type']) ) {
				$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
				$sel->addcond('M20.plan_type='.$searchList['plan_type']);
				$sel->addcond('M20.Remove=0');
			}
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// ----- ステータス変更 -----
		function U01S14($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['last_action_date_time']) ) {
				$dateStr = date('Y-m-d H:i:s', strtotime($searchList['last_action_date_time']));
				$sel->addcond('((last_action_date_time != 0 and last_action_date_time<'.$searchList['last_action_date_time'].') or (last_action_date_time is null and Idate<"'.$dateStr.'"))');
			}
			$sel->addcond('(forced_private=2 or alert_flag=1)');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- 未認証メール -----
		function U01S15($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('user_sex='.$searchList['user_sex']);
			}
			$sel->addcond('(identify_confirm=0 or identify_confirm=9)');
			$sel->addcond('plan_id=0');
			$sel->addcond('forced_private=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=0');
			if ( !empty($searchList['enrollment_date']) ) {
				$sel->addcond('enrollment_date='.$searchList['enrollment_date']);
			}
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- メールアドレス重複 -----
		function U01S16($user_id, $user_mailaddress) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			if ( $user_id > 0 ) {
				$sel->addcond('user_id<>'.$user_id);
			}
			$sel->addcond('user_mailaddress="'.$user_mailaddress.'"');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- メールアドレス重複 -----
		function U01S17($user_id, $Iuser) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			if ( $user_id > 0 ) {
				$sel->addcond('user_id<>'.$user_id);
			}
			$sel->addcond('Iuser="'.$Iuser.'"');
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- Paidy課金対象リスト -----
		function U01S18($date) {
			$date = intval($date);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M20.plan_title');
			$sel->addfield('M20.term_type');
			$sel->addfield('M20.plan_term');
			$sel->addfield('M20.plan_price');
			$sel->addfield('M20.renewal_plan_id');
			$sel->addfield('P06.paidy_token');
			$sel->addcond('U01.plan_id>0');
			$sel->addcond('U01.plan_expiry_date='.$date);
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addinner('P01_payments P01','P01.user_id=U01.user_id');
			$sel->addcond('P01.payment_type=6');
			$sel->addcond('P01.sales_item=0');
			$sel->addcond('P01.payment_result=1');
			$sel->addcond('P01.Remove=0');
			$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
			$sel->addcond('M20.Remove=0');
			$sel->addinner('P06_paidy_tokens P06','P06.user_id=U01.user_id');
			$sel->addcond('P06.Remove=0');
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// ----- ダウンロードリスト -----
		function U01S19($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex in ('.$searchList['user_sex'].')');
			}
			if ( !empty($searchList['redidence_prefecture_code']) ) {
				$sel->addcond('U01.redidence_prefecture_code in ('.$searchList['redidence_prefecture_code'].')');
			}
			if ( isset($searchList['user_status']) ) {
				$sel->addcond('U01.user_status in ('.$searchList['user_status'].')');
			}
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			if ( !empty($searchList['plan_type']) ) {
				$sel->addfield('M20.plan_title');
				$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
				$sel->addcond('M20.plan_type in ('.$searchList['plan_type'].')');
				$sel->addcond('M20.Remove=0');
			}
			if ( !empty($searchList['except_mail']) ) {
				$sel->addleftjoin('U04_mail U04', 'U04.user_id=U01.user_id and U04.mail_id in (' . $searchList['except_mail'] . ') and U04.deny_flag = 1 and U04.Remove = 0');
				$sel->addcond('U04.user_id IS NULL');
			}
			return $this->loadList($sel);
		}
		// ----- 退会処理リスト -----
		function U01S20($withdrawal_date, $flag_id) {
			$withdrawal_date = intval($withdrawal_date);
			$flag_id = intval($flag_id);
			if ( empty($withdrawal_date) || empty($flag_id) ) {
				return false;
			}
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('U01.withdrawal_date<='.$withdrawal_date);
			$sel->addcond('U01.user_status=9'); // 退会のみ（強制退会除く)
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			// ----- 処理済除外 -----
			$sel->addleftjoin('(select user_id from U36_flags where flag_id = ' . $flag_id . ' and flag_value = 1 and Remove = 0) U36','U36.user_id=U01.user_id');
			$sel->addcond('U36.user_id IS NULL');
			// ----- イエローカード除外 -----
			$sel->addleftjoin('(select user_id from U45_yellow_cards where Remove = 0) U45','U45.user_id=U01.user_id');
			$sel->addcond('U45.user_id IS NULL');
			return $this->loadList($sel);
		}
		// ----- 全項目検索条件 -----
		function U01S21($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['user_mailaddress']) ) {
				$sel->addcond('user_mailaddress="'.$searchList['user_mailaddress'].'"');
			}
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['forced_private']) ) {
				if ( $searchList['forced_private'] == 99 ) $searchList['forced_private'] = 0;
				$sel->addcond('forced_private='.$searchList['forced_private']);
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) $searchList['user_status'] = 0;
				$sel->addcond('user_status='.$searchList['user_status']);
			}
			if ( !empty($searchList['user_status_except']) ) {
				$sel->addcond('user_status!='.$searchList['user_status_except']);
			}
			if ( !empty($searchList['enrollment_yearmonth']) ) {
				$sel->addcond('LEFT(enrollment_date,6)='.$searchList['enrollment_yearmonth']);
			}
			if ( !empty($searchList['enrollment_date']) ) {
				$sel->addcond('enrollment_date='.$searchList['enrollment_date']);
			}
			if ( !empty($searchList['previous_enrollment_date']) ) {
				$sel->addcond('enrollment_date>'.$searchList['previous_enrollment_date']);
			}
			if ( !empty($searchList['withdrawal_yearmonth']) ) {
				$sel->addcond('LEFT(withdrawal_date,6)='.$searchList['withdrawal_yearmonth']);
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) $searchList['user_status'] = 0;
				$sel->addcond('user_status='.$searchList['user_status']);
			}
			if ( !empty($searchList['exclusion_user_status']) ) {
				if ( $searchList['exclusion_user_status'] == 99 ) $searchList['exclusion_user_status'] = 0;
				$sel->addcond('user_status<>'.$searchList['exclusion_user_status']);
			}
			if ( !empty($searchList['dummy_flag']) ) {
				if ( $searchList['dummy_flag'] == 99 ) $searchList['dummy_flag'] = 0;
				$sel->addcond('forced_private='.$searchList['dummy_flag']);
			}
			if ( !empty($searchList['last_action_date_time_start']) ) {
				$sel->addcond('last_action_date_time>='.$searchList['last_action_date_time_start']);
			}
			if ( !empty($searchList['last_action_date_time_finish']) ) {
				$sel->addcond('last_action_date_time<='.$searchList['last_action_date_time_finish']);
			}
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- search用 user_idのみ取得 -----
		function U01S22($selfU01, $searchList) {
			$sel = $this->CreateSSql(FALSE);
			$sel->addfield('U01.user_id');
			if ( !empty($searchList['search_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['search_sex']);
			}
			if ( $searchList['search_type'] == 1 ) {
				$sel->addorder('U01.last_action_date_time DESC');
				// if ( !empty($searchList['search_datetime']) ) {
				// 	$sel->addcond('U01.last_action_date_time<='.$searchList['search_datetime']);
				// }
			} else if ( $searchList['search_type'] == 2 ) {
				$sel->addorder('U01.user_id DESC');
			} else if ( $searchList['search_type'] == 3 ) {
				$sel->addorder('U01.last_profile_date_time DESC');
			}
			if ( !empty($searchList['search_spot_nickname']) ) {
				$sel->addcond('(U01.user_nickname like "%'.$searchList['search_spot_nickname'].'%")');
			}
			if ( !empty($searchList['search_spot_keyword']) ) {
				$sel->addcond('(U01.one_word like "%'.$searchList['search_spot_keyword'].'%" or U01.self_introduction like "%'.$searchList['search_spot_keyword'].'%")');
			}
			if ( !empty($searchList['search_spot_date_fee']) ) {
				$sel->addcond('(U01.about_date_fee like "%'.$searchList['search_spot_date_fee'].'%")');
			}
			if ( !empty($searchList['search_spot_desired_age']) ) {
				$sel->addcond('(U01.desired_age like "%'.$searchList['search_spot_desired_age'].'%")');
			}
			if ( !empty($searchList['search_age_start']) ) {
				$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) >=' . $searchList['search_age_start']);
			}
			if ( !empty($searchList['search_age_finish']) ) {
				$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) <=' . $searchList['search_age_finish']);
			}
			if ( !empty($searchList['search_prefecture_code']) ) {
				$sel->addcond('U01.redidence_prefecture_code in (' . $searchList['search_prefecture_code'] . ')');
			}
			if ( !empty($searchList['search_birth_prefecture_code']) ) {
				$sel->addcond('U01.birthplace_prefecture_code in (' . $searchList['search_birth_prefecture_code'] . ')');
			}
			if ( !empty($searchList['search_school_career_id']) ) {
				$sel->addcond('U01.school_career_id in (' . $searchList['search_school_career_id'] . ')');
			}
			if ( !empty($searchList['search_job_id']) ) {
				if ( strpos($searchList['search_job_id'], '999999999') !== false ) {

					// $array_job_id = explode(',', $searchList['search_job_id']);
					// $condstr = '';
					// foreach ( $array_job_id as $job_id ) {
					// 	if ( $job_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U25.job_id='.$job_id.' and U25.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U25_jobs U25', 'U25.user_id=U01.user_id and U25.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U25.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U25.user_id is NULL or ' . $condstr . ')');
					// }

					$valid_job_ids = array_filter(explode(',', $searchList['search_job_id']), fn($id) => $id != '999999999');
					if (empty($valid_job_ids)) {
						$sel->addcond('NOT EXISTS (SELECT 1 FROM U25_jobs WHERE U25_jobs.user_id = U01.user_id AND U25_jobs.Remove = 0)');
					} else {
						$in_clause = implode(',', array_map('intval', $valid_job_ids));
						$sel->addcond("(
							NOT EXISTS (
								SELECT 1 FROM U25_jobs
								WHERE U25_jobs.user_id = U01.user_id AND U25_jobs.Remove = 0
							)
							OR EXISTS (
								SELECT 1 FROM U25_jobs
								WHERE U25_jobs.user_id = U01.user_id
								AND U25_jobs.job_id IN ($in_clause)
								AND U25_jobs.Remove = 0
							)
						)");
					}


				} else {
					$sel->addcond('U01.user_id in (select user_id from U25_jobs where job_id in (' . $searchList['search_job_id'] . ') and Remove = 0)');
				}
			}
			if ( $searchList['search_income_id'] != "" ) {
				$sel->addcond('U01.income_id in (' . $searchList['search_income_id'] . ')');
			}
			if ( !empty($searchList['search_character_id']) ) {
				if ( strpos($searchList['search_character_id'], '999999999') !== false ) {

					// $array_character_id = explode(',', $searchList['search_character_id']);
					// $condstr = '';
					// foreach ( $array_character_id as $character_id ) {
					// 	if ( $character_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U12.character_id='.$character_id.' and U12.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U12_characters U12', 'U12.user_id=U01.user_id and U12.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U12.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U12.user_id is NULL or ' . $condstr . ')');
					// }

					$array_character_id = explode(',', $searchList['search_character_id']);
					$valid_character_ids = array_filter($array_character_id, fn($id) => $id != '999999999');
					if (empty($valid_character_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U12_characters
									WHERE U12_characters.user_id = U01.user_id
										AND U12_characters.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_character_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U12_characters
											WHERE U12_characters.user_id = U01.user_id
												AND U12_characters.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U12_characters
											WHERE U12_characters.user_id = U01.user_id
												AND U12_characters.character_id IN (' . $in_clause . ')
												AND U12_characters.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U12_characters where character_id in (' . $searchList['search_character_id'] . ') and Remove = 0)');
				}
			}
			if ( $searchList['search_personality_id'] != "" ) {
				$sel->addcond('U01.personality_id in (' . $searchList['search_personality_id'] . ')');
			}
			if ( !empty($searchList['search_no_entry_height']) ) {
				if ( !empty($searchList['search_height_start']) ) {
					$sel->addcond('(U01.user_height >=' . $searchList['search_height_start'] . ' or U01.user_height = 0)');
				}
				if ( !empty($searchList['search_height_finish']) ) {
					$sel->addcond('(U01.user_height <=' . $searchList['search_height_finish'] . ' or U01.user_height = 0)');
				}
				if ( empty($searchList['search_height_start']) && empty($searchList['search_height_finish']) ) {
					$sel->addcond('U01.user_height = 0');
				}
			} else {
				if ( !empty($searchList['search_height_start']) ) {
					$sel->addcond('U01.user_height >=' . $searchList['search_height_start']);
				}
				if ( !empty($searchList['search_height_finish']) ) {
					$sel->addcond('U01.user_height <=' . $searchList['search_height_finish']);
				}
			}
			if ( !empty($searchList['search_body_id']) ) {
				if ( strpos($searchList['search_body_id'], '999999999') !== false ) {


					// $array_body_id = explode(',', $searchList['search_body_id']);
					// $condstr = '';
					// foreach ( $array_body_id as $body_id ) {
					// 	if ( $body_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U13.body_id='.$body_id.' and U13.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U13_bodys U13', 'U13.user_id=U01.user_id and U13.sex_id= '.$searchList['search_sex'].' and U13.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U13.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U13.user_id is NULL or ' . $condstr . ')');
					// }
					$array_body_id = explode(',', $searchList['search_body_id']);
					$valid_body_ids = array_filter($array_body_id, fn($id) => $id != '999999999');

					if (empty($valid_body_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U13_bodys
									WHERE U13_bodys.user_id = U01.user_id
										AND U13_bodys.sex_id = ' . $searchList['search_sex'] . '
										AND U13_bodys.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_body_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U13_bodys
											WHERE U13_bodys.user_id = U01.user_id
												AND U13_bodys.sex_id = ' . $searchList['search_sex'] . '
												AND U13_bodys.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U13_bodys
											WHERE U13_bodys.user_id = U01.user_id
												AND U13_bodys.sex_id = ' . $searchList['search_sex'] . '
												AND U13_bodys.body_id IN (' . $in_clause . ')
												AND U13_bodys.Remove = 0
									)
							)');
					}


				} else {
					$sel->addcond('U01.user_id in (select user_id from U13_bodys where body_id in (' . $searchList['search_body_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( $searchList['search_blood'] != "" ) {
				$sel->addcond('U01.user_blood in (' . $searchList['search_blood'] . ')');
			}
			if ( $searchList['search_alcohol_id'] != "" ) {
				$sel->addcond('U01.alcohol_id in (' . $searchList['search_alcohol_id'] . ')');
			}
			if ( $searchList['search_cigarette_id'] != "" ) {
				$sel->addcond('U01.cigarette_id in (' . $searchList['search_cigarette_id'] . ')');
			}
			if ( $searchList['search_housemate_id'] != "" ) {
				$sel->addcond('U01.housemate_id in (' . $searchList['search_housemate_id'] . ')');
			}
			if ( $searchList['search_family_relationship_id'] != "" ) {
				$sel->addcond('U01.family_relationship_id in (' . $searchList['search_family_relationship_id'] . ')');
			}
			if ( $searchList['search_family_continuation_relationship_id'] != "" ) {
				$sel->addcond('U01.family_continuation_relationship_id in (' . $searchList['search_family_continuation_relationship_id'] . ')');
			}
			if ( $searchList['search_child_id'] != "" ) {
				$sel->addcond('U01.child_id in (' . $searchList['search_child_id'] . ')');
			}
			if ( !empty($searchList['search_hobby_id']) ) {
				if ( strpos($searchList['search_hobby_id'], '999999999') !== false ) {

					// $array_hobby_id = explode(',', $searchList['search_hobby_id']);
					// $condstr = '';
					// foreach ( $array_hobby_id as $hobby_id ) {
					// 	if ( $hobby_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U14.hobby_id='.$hobby_id.' and U14.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U14_hobbies U14', 'U14.user_id=U01.user_id and U14.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U14.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U14.user_id is NULL or ' . $condstr . ')');
					// }

					$array_hobby_id = explode(',', $searchList['search_hobby_id']);
					$valid_hobby_ids = array_filter($array_hobby_id, fn($id) => $id != '999999999');

					if (empty($valid_hobby_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U14_hobbies
									WHERE U14_hobbies.user_id = U01.user_id
										AND U14_hobbies.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_hobby_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U14_hobbies
											WHERE U14_hobbies.user_id = U01.user_id
												AND U14_hobbies.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U14_hobbies
											WHERE U14_hobbies.user_id = U01.user_id
												AND U14_hobbies.hobby_id IN (' . $in_clause . ')
												AND U14_hobbies.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U14_hobbies where hobby_id in (' . $searchList['search_hobby_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_relation_id']) ) {
				if ( strpos($searchList['search_favorite_relation_id'], '999999999') !== false ) {

					// $array_favorite_relation_id = explode(',', $searchList['search_favorite_relation_id']);
					// $condstr = '';
					// foreach ( $array_favorite_relation_id as $favorite_relation_id ) {
					// 	if ( $favorite_relation_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U18.favorite_relation_id='.$favorite_relation_id.' and U18.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U18_favorite_relations U18', 'U18.user_id=U01.user_id and U18.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U18.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U18.user_id is NULL or ' . $condstr . ')');
					// }

					$array_favorite_relation_id = explode(',', $searchList['search_favorite_relation_id']);
					$valid_ids = array_filter($array_favorite_relation_id, fn($id) => $id != '999999999');
					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U18_favorite_relations
									WHERE U18_favorite_relations.user_id = U01.user_id
										AND U18_favorite_relations.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U18_favorite_relations
											WHERE U18_favorite_relations.user_id = U01.user_id
												AND U18_favorite_relations.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U18_favorite_relations
											WHERE U18_favorite_relations.user_id = U01.user_id
												AND U18_favorite_relations.favorite_relation_id IN (' . $in_clause . ')
												AND U18_favorite_relations.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U18_favorite_relations where favorite_relation_id in (' . $searchList['search_favorite_relation_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_time_zone_id']) ) {
				if ( strpos($searchList['search_favorite_time_zone_id'], '999999999') !== false ) {


					// $array_favorite_time_zone_id = explode(',', $searchList['search_favorite_time_zone_id']);
					// $condstr = '';
					// foreach ( $array_favorite_time_zone_id as $favorite_time_zone_id ) {
					// 	if ( $favorite_time_zone_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U19.favorite_time_zone_id='.$favorite_time_zone_id.' and U19.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U19_favorite_time_zones U19', 'U19.user_id=U01.user_id and U19.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U19.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U19.user_id is NULL or ' . $condstr . ')');
					// }

					$array_favorite_time_zone_id = explode(',', $searchList['search_favorite_time_zone_id']);
					$valid_ids = array_filter($array_favorite_time_zone_id, fn($id) => $id != '999999999');

					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U19_favorite_time_zones
									WHERE U19_favorite_time_zones.user_id = U01.user_id
										AND U19_favorite_time_zones.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U19_favorite_time_zones
											WHERE U19_favorite_time_zones.user_id = U01.user_id
												AND U19_favorite_time_zones.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U19_favorite_time_zones
											WHERE U19_favorite_time_zones.user_id = U01.user_id
												AND U19_favorite_time_zones.favorite_time_zone_id IN (' . $in_clause . ')
												AND U19_favorite_time_zones.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U19_favorite_time_zones where favorite_time_zone_id in (' . $searchList['search_favorite_time_zone_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_meet_process_id']) ) {
				if ( strpos($searchList['search_meet_process_id'], '999999999') !== false ) {


					// $array_meet_process_id = explode(',', $searchList['search_meet_process_id']);
					// $condstr = '';
					// foreach ( $array_meet_process_id as $meet_process_id ) {
					// 	if ( $meet_process_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U39.meet_process_id='.$meet_process_id.' and U39.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U39_meet_processes U39', 'U39.user_id=U01.user_id and U39.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U39.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U39.user_id is NULL or ' . $condstr . ')');
					// }
					$array_meet_process_id = explode(',', $searchList['search_meet_process_id']);
					$valid_ids = array_filter($array_meet_process_id, fn($id) => $id != '999999999');
					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U39_meet_processes
									WHERE U39_meet_processes.user_id = U01.user_id
										AND U39_meet_processes.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U39_meet_processes
											WHERE U39_meet_processes.user_id = U01.user_id
												AND U39_meet_processes.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U39_meet_processes
											WHERE U39_meet_processes.user_id = U01.user_id
												AND U39_meet_processes.meet_process_id IN (' . $in_clause . ')
												AND U39_meet_processes.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U39_meet_processes where meet_process_id in (' . $searchList['search_meet_process_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_visual_id']) ) {
				if ( strpos($searchList['search_favorite_visual_id'], '999999999') !== false ) {


					// $array_favorite_visual_id = explode(',', $searchList['search_favorite_visual_id']);
					// $condstr = '';
					// foreach ( $array_favorite_visual_id as $favorite_visual_id ) {
					// 	if ( $favorite_visual_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U15.favorite_visual_id='.$favorite_visual_id.' and U15.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U15_favorite_visuals U15', 'U15.user_id=U01.user_id and U15.sex_id='.$searchList['search_sex'].' and U15.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U15.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U15.user_id is NULL or ' . $condstr . ')');
					// }
					$array_favorite_visual_id = explode(',', $searchList['search_favorite_visual_id']);
					$valid_ids = array_filter($array_favorite_visual_id, fn($id) => $id != '999999999');

					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U15_favorite_visuals
									WHERE U15_favorite_visuals.user_id = U01.user_id
										AND U15_favorite_visuals.sex_id = ' . $searchList['search_sex'] . '
										AND U15_favorite_visuals.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U15_favorite_visuals
											WHERE U15_favorite_visuals.user_id = U01.user_id
												AND U15_favorite_visuals.sex_id = ' . $searchList['search_sex'] . '
												AND U15_favorite_visuals.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U15_favorite_visuals
											WHERE U15_favorite_visuals.user_id = U01.user_id
												AND U15_favorite_visuals.favorite_visual_id IN (' . $in_clause . ')
												AND U15_favorite_visuals.sex_id = ' . $searchList['search_sex'] . '
												AND U15_favorite_visuals.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U15_favorite_visuals where favorite_visual_id in (' . $searchList['search_favorite_visual_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_character_id']) ) {
				if ( strpos($searchList['search_favorite_character_id'], '999999999') !== false ) {

					// $array_favorite_character_id = explode(',', $searchList['search_favorite_character_id']);
					// $condstr = '';
					// foreach ( $array_favorite_character_id as $favorite_character_id ) {
					// 	if ( $favorite_character_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U17.favorite_character_id='.$favorite_character_id.' and U17.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U17_favorite_characters U17', 'U17.user_id=U01.user_id and U17.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U17.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U17.user_id is NULL or ' . $condstr . ')');
					// }
					$array_favorite_character_id = explode(',', $searchList['search_favorite_character_id']);
					$valid_ids = array_filter($array_favorite_character_id, fn($id) => $id != '999999999');

					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U17_favorite_characters
									WHERE U17_favorite_characters.user_id = U01.user_id
										AND U17_favorite_characters.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U17_favorite_characters
											WHERE U17_favorite_characters.user_id = U01.user_id
												AND U17_favorite_characters.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U17_favorite_characters
											WHERE U17_favorite_characters.user_id = U01.user_id
												AND U17_favorite_characters.favorite_character_id IN (' . $in_clause . ')
												AND U17_favorite_characters.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U17_favorite_characters where favorite_character_id in (' . $searchList['search_favorite_character_id'] . ') and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_favorite_body_id']) ) {
				if ( strpos($searchList['search_favorite_body_id'], '999999999') !== false ) {

					// $array_favorite_body_id = explode(',', $searchList['search_favorite_body_id']);
					// $condstr = '';
					// foreach ( $array_favorite_body_id as $favorite_body_id ) {
					// 	if ( $favorite_body_id != 999999999 ) {
					// 		if ( !empty($condstr) ) {
					// 			$condstr .= ' or ';
					// 		}
					// 		$condstr .= '(U16.favorite_body_id='.$favorite_body_id.' and U16.Remove=0)';
					// 	}
					// }
					// $sel->addleftjoin('U16_favorite_bodys U16', 'U16.user_id=U01.user_id and U16.sex_id='.$searchList['search_sex'].' and U16.Remove=0');
					// if ( empty($condstr) ) {
					// 	$sel->addcond('U16.user_id is NULL');
					// } else {
					// 	$sel->addcond('(U16.user_id is NULL or ' . $condstr . ')');
					// }
					$array_favorite_body_id = explode(',', $searchList['search_favorite_body_id']);
					$valid_ids = array_filter($array_favorite_body_id, fn($id) => $id != '999999999');

					if (empty($valid_ids)) {
							$sel->addcond('NOT EXISTS (
									SELECT 1 FROM U16_favorite_bodys
									WHERE U16_favorite_bodys.user_id = U01.user_id
										AND U16_favorite_bodys.sex_id = ' . $searchList['search_sex'] . '
										AND U16_favorite_bodys.Remove = 0
							)');
					} else {
							$in_clause = implode(',', array_map('intval', $valid_ids));
							$sel->addcond('(
									NOT EXISTS (
											SELECT 1 FROM U16_favorite_bodys
											WHERE U16_favorite_bodys.user_id = U01.user_id
												AND U16_favorite_bodys.sex_id = ' . $searchList['search_sex'] . '
												AND U16_favorite_bodys.Remove = 0
									)
									OR EXISTS (
											SELECT 1 FROM U16_favorite_bodys
											WHERE U16_favorite_bodys.user_id = U01.user_id
												AND U16_favorite_bodys.favorite_body_id IN (' . $in_clause . ')
												AND U16_favorite_bodys.sex_id = ' . $searchList['search_sex'] . '
												AND U16_favorite_bodys.Remove = 0
									)
							)');
					}

				} else {
					$sel->addcond('U01.user_id in (select user_id from U16_favorite_bodys where favorite_body_id in (' . $searchList['search_favorite_body_id'] . ') and sex_id = ' . $searchList['search_sex'] . ' and Remove = 0)');
				}
			}
			if ( !empty($searchList['search_omit_like']) ) {
				$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $selfU01['user_id'] . ') U08','U08.user_id=U01.user_id');
				$sel->addcond('U08.user_id IS NULL');
			}
			if ( !empty($searchList['search_omit_give_like']) ) {
				$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $selfU01['user_id'] . ') U0802','U0802.like_user_id=U01.user_id');
				$sel->addcond('U0802.like_user_id IS NULL');
			}
			if ( !empty($searchList['search_omit_match']) ) {
				$sel->addleftjoin('(select match_user_id from U09_matches where user_id = ' . $selfU01['user_id'] . ') U09','U09.match_user_id=U01.user_id');
				$sel->addcond('U09.match_user_id IS NULL');
			}
			// // ----- メイン画像 -----
			// $sel->addleftjoin('U02_user_images U02M', 'U02M.user_id=U01.user_id and U02M.image_id = 1 and U02M.Remove=0');
			// if ( !empty($searchList['my_user_id']) ) {
			// 	$sel->addleftjoin('U59_nicknames U59', 'U59.user_id='.$searchList['my_user_id'].' and U59.nickname_user_id = U01.user_id and U59.Remove=0');
			// }
			// // ----- オンライン表示 -----
			// $sel->addleftjoin('U04_mail U04', 'U04.user_id=U01.user_id and U04.mail_id = 12 and U04.Remove=0');
			// // ----- PREMIUM表示 -----
			// $sel->addleftjoin('U36_flags U36', 'U36.user_id=U01.user_id and U36.flag_id = 6 and U36.Remove=0');


			// ----- ごめんなさい除外 -----
			// $sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $selfU01['user_id'] . ' and is_no_like = 1) U0803','U0803.like_user_id=U01.user_id');
			// $sel->addcond('U0803.like_user_id IS NULL');
			// $sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $selfU01['user_id'] . ' and is_no_like = 1) U0804','U0804.user_id=U01.user_id');
			// $sel->addcond('U0804.user_id IS NULL');
			$sel->addcond('NOT EXISTS (SELECT 1 FROM U08_likes U0803 WHERE user_id = ' . $selfU01['user_id'] . ' AND is_no_like = 1 AND U0803.like_user_id = U01.user_id)');
			$sel->addcond('NOT EXISTS (SELECT 1 FROM U08_likes U0804 WHERE like_user_id = ' . $selfU01['user_id'] . ' AND is_no_like = 1 AND U0804.user_id = U01.user_id)');

			// ----- マッチングした相手除外（お蔵入り） -----
//			$sel->addleftjoin('(select match_user_id from U09_matches U09IN01 inner join U01_users U01IN01 on (U01IN01.user_id = U09IN01.match_user_id and U01IN01.is_match_hide = 1) where U09IN01.user_id = ' . $selfU01['user_id'] . ') U0901','U0901.match_user_id=U01.user_id');
//			$sel->addcond('U0901.match_user_id IS NULL');
			// ----- いいねした相手除外（お蔵入り） -----
//			$sel->addleftjoin('(select like_user_id from U08_likes U08IN01 inner join U01_users U01IN01 on (U01IN01.user_id = U08IN01.like_user_id and U01IN01.is_like_hide = 1) where U08IN01.user_id = ' . $selfU01['user_id'] . ') U0801','U0801.like_user_id=U01.user_id');
//			$sel->addcond('U0801.like_user_id IS NULL');
			if ( !empty($searchList['search_identify_confirm']) ) {
				$sel->addcond('U01.identify_confirm = 1');
			}
			if ( !empty($searchList['search_is_main_image']) ) {
				$sel->addinner('U02_user_images U02','U02.user_id=U01.user_id');
				$sel->addcond('U02.image_type=1');
				$sel->addcond('U02.image_extension!=""');
				$sel->addcond('U02.is_unpublish=0');
				$sel->addcond('U02.Remove=0');
			}
			$sel->addcond('U01.none_public_status=0');
			if ( empty($selfU01['forced_private']) ) {
				$sel->addcond('U01.forced_private=0');	// フラグの持ち方要注意 0だったら入る
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addgroup('U01.user_id');
			// $sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			if ( !empty($searchList['search_limit']) ) {
				$sel->setoffset($searchList['search_offset']);
				$sel->setlimit($searchList['search_limit']);
			}
			// ----- ブロックリスト除外 -----
			// $sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $selfU01['user_id'] . ') U24','U24.block_user_id=U01.user_id');
			// $sel->addcond('U24.block_user_id IS NULL');
			$sel->addcond('NOT EXISTS (SELECT 1 FROM U24_block_targets U24 WHERE user_id = ' . $selfU01['user_id'] . ' AND U24.block_user_id = U01.user_id)');
			// ----- 非表示リスト除外 -----
			$sel->addcond('NOT EXISTS (SELECT 1 FROM U61_hide_users U61 WHERE user_id = ' . $selfU01['user_id'] . ' AND U61.hide_user_id = U01.user_id)');

//			$sel->addcond('NOT EXISTS ( SELECT U24.block_user_id FROM U24_block_targets U24 WHERE U24.user_id=' . $selfU01['user_id'] . ' and U24.block_user_id=U01.user_id LIMIT 1 )');
//print($sel->getsql());
//error_log($sel->getsql());
  //  error_log(basename(__FILE__).__LINE__.'|'.$sel->getsql());
			return $this->loadList($sel);
		}
		// ----- search用 user_idsで検索 -----
		function U01S23($searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U02M.image_id');
			$sel->addfield('U02M.image_code');
			$sel->addfield('U02M.image_extension');
			$sel->addfield('U02M.is_unpublish');
			$sel->addfield('U02M.publish_version_id');
			$sel->addfield('U02M.thumbnail_version_id');
			$sel->addfield('U02M.Udate as U02_Udate');
			$sel->addfield('U04.deny_flag');
			$sel->addfield('U36.flag_value');
			$sel->addfield('U59.nickname');
			$sel->addfield('M20.plan_type');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02M', 'U02M.user_id=U01.user_id and U02M.image_id = 1 and U02M.Remove=0');
			if ( !empty($searchList['my_user_id']) ) {
				$sel->addleftjoin('U59_nicknames U59', 'U59.user_id='.$searchList['my_user_id'].' and U59.nickname_user_id = U01.user_id and U59.Remove=0');
			}
			// ----- オンライン表示 -----
			$sel->addleftjoin('U04_mail U04', 'U04.user_id=U01.user_id and U04.mail_id = 12 and U04.Remove=0');
			// ----- PREMIUM表示 -----
			$sel->addleftjoin('U36_flags U36', 'U36.user_id=U01.user_id and U36.flag_id = 6 and U36.Remove=0');
			$sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			// ----- userIDによる検索 -----
			if (empty($searchList['userIds'])) {
				return [];
			} else {
				$userIds = implode(',',$searchList['userIds']);
				$sel->addcond('U01.user_id in ('.$userIds.')');
				$sel->addorder("FIELD(U01.user_id, $userIds)");
			}
			return $this->loadList($sel);
		}
		// ----- PayPal課金対象リスト -----
		function U01S24($date) {
			$date = intval($date);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M20.plan_title');
			$sel->addfield('M20.term_type');
			$sel->addfield('M20.plan_term');
			$sel->addfield('M20.plan_price');
			$sel->addfield('M20.renewal_plan_id');
			$sel->addfield('P07.paypal_token');
			$sel->addcond('U01.plan_id>0');
			$sel->addcond('U01.plan_expiry_date='.$date);
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addinner('P01_payments P01','P01.user_id=U01.user_id');
			$sel->addcond('P01.payment_type=7');
			$sel->addcond('P01.sales_item=0');
			$sel->addcond('P01.payment_result=1');
			$sel->addcond('P01.Remove=0');
			$sel->addinner('M20_plans M20','M20.plan_id=U01.plan_id');
			$sel->addcond('M20.Remove=0');
			$sel->addinner('P07_paypal_tokens P07','P07.user_id=U01.user_id');
			$sel->addcond('P07.Remove=0');
			$sel->addgroup('U01.user_id');
			return $this->loadList($sel);
		}
		// 件数
		function U01C01($searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['except_self']) && !empty($searchList['user_id']) ) {
				$sel->addcond('U01.user_id!='.$searchList['user_id']);
			}
			if ( !empty($searchList['keyword']) ) {
				$sel->addcond('(U01.user_code="'.$searchList['keyword'].'" or U01.user_mailaddress like "%' .$searchList['keyword'].'%" or U01.user_nickname like "'.$searchList['keyword'].'%")');
			}
			if ( !empty($searchList['user_mailaddress']) ) {
				$sel->addcond('U01.user_mailaddress="'.$searchList['user_mailaddress'].'"');
			}
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['user_age_start']) ) {
				$sel->addcond('U01.user_birthday<='.(date('Ymd') - $searchList['user_age_start'] * 10000));
			}
			if ( !empty($searchList['user_age_finish']) ) {
				$sel->addcond('U01.user_birthday>='.(date('Ymd') - ($searchList['user_age_finish'] + 1) * 10000));
			}
			if ( !empty($searchList['user_birthday']) ) {
				$searchList['user_birthday'] = date('Ymd', strtotime($searchList['user_birthday']));
				$sel->addcond('U01.user_birthday='.$searchList['user_birthday']);
			}
			if ( !empty($searchList['redidence_prefecture_code']) ) {
				if ( $searchList['redidence_prefecture_code'] == 99 ) {
					$sel->addcond('U01.redidence_prefecture_code=0');
				} else {
					$sel->addcond('U01.redidence_prefecture_code='.$searchList['redidence_prefecture_code']);
				}
			}
			if ( !empty($searchList['plan_type']) ) {
				$sel->addleftjoin('M20_plans M20','M20.plan_id=U01.plan_id and M20.Remove=0');
				$sel->addcond('M20.plan_type='.$searchList['plan_type']);
			}
			if ( !empty($searchList['identify_confirm']) ) {
				if ( $searchList['identify_confirm'] == 99 ) $searchList['identify_confirm'] = 0;
				$sel->addcond('U01.identify_confirm='.$searchList['identify_confirm']);
			}
			if ( !empty($searchList['user_status']) ) {
				if ( $searchList['user_status'] == 99 ) {
					$sel->addcond('U01.user_status>0');
				} else if ( $searchList['user_status'] == 98 ) {
					$sel->addcond('U01.user_status!=10');
				} else {
					if ( $searchList['user_status'] == 1 ) $searchList['user_status'] = 0;
					$sel->addcond('U01.user_status='.$searchList['user_status']);
				}
			}
			if ( !empty($searchList['private']) ) {
				if ( $searchList['private'] == 1 ) {
					$sel->addcond('U01.none_public_status=1');
				} else if ( $searchList['private'] == 11 ) {
					$sel->addcond('U01.forced_private=1');
				} else if ( $searchList['private'] == 12 ) {
					$sel->addcond('U01.forced_private=2');
				} else if ( $searchList['private'] == 13 ) {
					$sel->addcond('U01.forced_private=3');
				} else if ( $searchList['private'] == 21 ) {
					$sel->addcond('U01.forced_private!=1');
				} else if ( $searchList['private'] == 22 ) {
					$sel->addcond('U01.none_public_status=0');
					$sel->addcond('U01.forced_private=0');
				} else if ( $searchList['private'] == 23 ) {
					$sel->addcond('U01.forced_private=0');
				}
			}
			if ( !empty($searchList['enrollment_date_start']) ) {
				$enrollment_date_start = date('Ymd', strtotime($searchList['enrollment_date_start']));
				$sel->addcond('U01.enrollment_date>='.$enrollment_date_start);
			}
			if ( !empty($searchList['enrollment_date_finish']) ) {
				$enrollment_date_finish = date('Ymd', strtotime($searchList['enrollment_date_finish']));
				$sel->addcond('U01.enrollment_date<='.$enrollment_date_finish);
			}
			if ( !empty($searchList['dummy_flag']) ) {
				if ( $searchList['dummy_flag'] == 9 ) {
					$sel->addcond('U01.dummy_flag=0');
				} else if ( !empty($searchList['dummy_flag']) ) {
					$sel->addcond('U01.dummy_flag='.$searchList['dummy_flag']);
				}
			}
			if ( !empty($searchList['alert_flag']) ) {
				$alert_flag = $searchList['alert_flag'];
				if ( $searchList['alert_flag'] == 9 ) {
					$alert_flag = 1;
					$sel->addcond('last_talk_date_time>0');
					$sel->addcond('last_talk_date_time IS NOT NULL');
				}
				$sel->addcond('U01.alert_flag='.$alert_flag);
			}
			if ( !empty($searchList['warning_flag']) ) {
				$sel->addcond('U01.warning_flag='.$searchList['warning_flag']);
			}
			if ( !empty($searchList['scam']) ) {
				if ( $searchList['scam'] == 1 ) {
					$sel->addcond('U01.un_scam=0');
					$sel->addinner('W17_scam_lists W17', '(W17.scam_mailaddress=U01.user_mailaddress or W17.scam_ipaddress=U01.Iuser)');
					$sel->addcond('W17.Remove=0');
				} else if ( $searchList['scam'] == 2 ) {
					$sel->addcond('U01.one_word="初めまして,よろしくお願いします"');
					$sel->addcond('U01.user_nickname REGEXP "^[a-zA-Z]+$"');
					$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
					$sel->addcond('W07.media=0');
					$sel->addcond('W07.Remove=0');
				}
			}
			if ( !empty($searchList['un_scam']) ) {
				if ( $searchList['un_scam'] == 99 ) $searchList['un_scam'] = 0;
				$sel->addcond('U01.un_scam='.$searchList['un_scam']);
			}
			if ( !empty($searchList['user_nickname']) ) {
				$sel->addcond('(U01.user_nickname like "'.$searchList['user_nickname'].'%"');
			}
			if ( !empty($searchList['media']) ) {
				$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
				if ( $searchList['media'] == 999999999 ) {
					$sel->addcond('W07.media=0');
				} else {
					$sel->addcond('W07.media='.$searchList['media']);
				}
				$sel->addcond('W07.Remove=0');
			}
			if ( !empty($searchList['ipaddress']) ) {
				$sel->addcond('U01.Iuser="'.$searchList['ipaddress'].'"');
			}
			if ( !empty($searchList['profile']) ) {
				//キーワード検索（スペースで分割して、and検索）
				$condkeyword = seach_split("concat(U01.one_word, U01.self_introduction, U01.user_description)", $searchList['profile'], 'and');
				$sel->addcond($condkeyword);
			}
			if ( !empty($searchList['order_id']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.order_id='.$searchList['order_id']);
				$sel->addcond('P01.Remove=0');
			}
			if ( !empty($searchList['payment']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.price>0');
				$sel->addcond('P01.payment_result=1');
				$sel->addcond('P01.tran_date_time>='.date('YmdHis', mktime(0, 0, 0, date('m') - 1, date('d'), date('Y'))));
				$sel->addcond('P01.Remove=0');
			}
			if ( !empty($searchList['yellow_card']) ) {
				$sel->addinner('U45_yellow_cards U45', 'U45.user_id=U01.user_id');
				$sel->addcond('U45.Remove=0');
			}
			if ( !empty($searchList['alert_flag']) && $searchList['alert_flag'] == 9 ) {
				if (empty($searchList['include_scam_judge'])) $sel->addcond('U51.scam_judge != 1'); //「1:未判断」を除く
				$sel->addleftjoin('U51_scam_index U51', 'U51.user_id=U01.user_id');
			}			
			if ( !empty($searchList['phone']) ) {
				$sel->addinner('U49_two_factors U49', 'U49.user_id=U01.user_id');
				$sel->addcond('U49.phone_number="'.preg_replace("/[^0-9]/", "", $searchList['phone']).'"');
				$sel->addcond('U49.is_confirm=1');
				$sel->addcond('U49.Remove=0');
			}
			$sel->addcond('U01.Remove=0');
			if ( !empty($searchList['monitor']) ) {
				$sel->addinner('A14_monitoring_users A14', 'A14.user_id=U01.user_id');
				$sel->addcond('A14.administrator_id='.$_SESSION['administrator_id']);
				$sel->addcond('A14.monitoring_status='.$searchList['monitor']);
				$sel->addcond('A14.Remove=0');
			}
//			print($sel->getsql());
			return $this->loadCalcResult($sel, 'COUNT(DISTINCT(U01.user_id))');
		}
		// ----- 登録件数 -----
		function U01C02($start_date, $finish_date, $user_sex) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('user_sex='.$user_sex);
			}
			if ( !empty($start_date) ) {
				$sel->addcond('enrollment_date>='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('enrollment_date<'.$finish_date);
			}
			$sel->addcond('forced_private=0');
			$sel->addcond('user_status!=10');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(user_id)');
		}
		// ----- 年代件数 -----
		function U01C03($start_date, $finish_date, $user_sex, $enrollment_date, $enrollment_month, $chargeFlag) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('U01.user_sex='.$user_sex);
			}
			if ( !empty($start_date) ) {
				$sel->addcond('U01.user_birthday<='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('U01.user_birthday>='.$finish_date);
			}
			if ( !empty($enrollment_date) ) {
				$sel->addcond('U01.enrollment_date='.$enrollment_date);
			}
			if ( !empty($enrollment_month) ) {
				$sel->addcond('LEFT(U01.enrollment_date,6)='.$enrollment_month);
			}
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			if ( $chargeFlag ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.payment_result=1');
				$sel->addcond('P01.Remove=0');
			}
			return $this->loadCalcResult($sel, 'COUNT(distinct(U01.user_id))');
		}
		// ----- 退会件数 -----
		function U01C04($start_date, $finish_date, $user_sex, $user_status, $forced_private, $dummy_flag) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$user_sex = intval($user_sex);
			$user_status = intval($user_status);
			$forced_private = intval($forced_private);
			$dummy_flag = intval($dummy_flag);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('user_sex='.$user_sex);
			}
			$sel->addcond('user_status='.$user_status);
			if ( !empty($start_date) ) {
				$sel->addcond('withdrawal_date>='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('withdrawal_date<'.$finish_date);
			}
			if ( !empty($forced_private) ) {
				if ( $forced_private == 99 ) $forced_private = 0;
				$sel->addcond('U01.forced_private='.$forced_private);
			}
			if ( !empty($dummy_flag) ) {
				if ( $dummy_flag == 99 ) $dummy_flag = 0;
				$sel->addcond('dummy_flag='.$dummy_flag);
			}
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(user_id)');
		}
		// ----- 登録件数 -----
		function U01C05($start_date, $finish_date, $user_sex) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('user_sex='.$user_sex);
			}
			if ( !empty($start_date) ) {
				$sel->addcond('enrollment_date>='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('enrollment_date<'.$finish_date);
			}
			$sel->addcond('forced_private>0');
			$sel->addcond('user_status!=10');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(user_id)');
		}
		// ----- 有効件数 -----
		function U01C06($start_date, $finish_date, $user_sex) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$user_sex = intval($user_sex);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('user_sex='.$user_sex);
			}
			if ( !empty($start_date) ) {
				$sel->addcond('enrollment_date>='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('enrollment_date<='.$finish_date);
				$sel->addcond('(withdrawal_date=0 or withdrawal_date>'.$finish_date.')');
			}
			$sel->addcond('forced_private=0');
			$sel->addcond('user_status=0');
			$sel->addcond('dummy_flag=0');
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(user_id)');
		}
		// ----- 都道府県件数 -----
		function U01C07($start_date, $finish_date, $enrollment_month, $redidence_prefecture_code, $user_sex, $media) {
			$start_date = intval($start_date);
			$finish_date = intval($finish_date);
			$enrollment_month = intval($enrollment_month);
			$redidence_prefecture_code = intval($redidence_prefecture_code);
			$user_sex = intval($user_sex);
			$media = intval($media);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($user_sex) ) {
				$sel->addcond('U01.user_sex='.$user_sex);
			}
			if ( !empty($redidence_prefecture_code) ) {
				$sel->addcond('U01.redidence_prefecture_code='.$redidence_prefecture_code);
			}
			if ( !empty($start_date) ) {
				$sel->addcond('U01.enrollment_date>='.$start_date);
			}
			if ( !empty($finish_date) ) {
				$sel->addcond('U01.enrollment_date<='.$finish_date);
			}
			if ( !empty($enrollment_month) ) {
				$sel->addcond('LEFT(U01.enrollment_date,6)='.$enrollment_month);
			}
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			if ( !empty($media) ) {
				$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
				if ( $media == 999999999 ) {
					$sel->addcond('W07.media=0');
				} else {
					$sel->addcond('W07.media='.$media);
				}
			}
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		function U01C08($selfU01, $searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['search_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['search_sex']);
			}
			if ( $searchList['search_type'] == 2 ) {
				$sel->addorder('U01.Idate DESC');
			} else if ( $searchList['search_type'] == 3 ) {
				if ( !empty($searchList['search_age_start']) ) {
					$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) >=' . $searchList['search_age_start']);
				}
				if ( !empty($searchList['search_age_finish']) ) {
					$sel->addcond('TIMESTAMPDIFF(YEAR, user_birthday, CURDATE()) <=' . $searchList['search_age_finish']);
				}
				if ( !empty($searchList['search_prefecture_code']) ) {
					$sel->addcond('U01.redidence_prefecture_code in (' . $searchList['search_prefecture_code'] . ')');
				}
				if ( !empty($searchList['search_birth_prefecture_code']) ) {
					$sel->addcond('U01.birthplace_prefecture_code in (' . $searchList['search_birth_prefecture_code'] . ')');
				}
			}
			$sel->addcond('U01.none_public_status=0');
			if ( empty($selfU01['forced_private']) ) {
				$sel->addcond('U01.forced_private=0');	// フラグの持ち方要注意 0だったら入る
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			if ( !empty($searchList['search_limit']) ) {
				$sel->setoffset($searchList['search_offset']);
				$sel->setlimit($searchList['search_limit']);
			}
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $selfU01['user_id'] . ') U24','U24.block_user_id=U01.user_id');
			$sel->addcond('U24.block_user_id IS NULL');
//			$sel->addcond('NOT EXISTS ( SELECT U24.block_user_id FROM U24_block_targets U24 WHERE U24.user_id=' . $selfU01['user_id'] . ' and U24.block_user_id=U01.user_id LIMIT 1 )');
//			print($sel->getsql());
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		// ----- LTV集計用（searchListの順番大切） -----
		function U01C09($searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			$sel->addcond('U01.forced_private=0');
			if ( !empty($searchList['user_status']) ) {
				$sel->addcond('U01.user_status='.$searchList['user_status']);
			}
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			if ( !empty($searchList['enrollment_yearmonth']) ) {
				$sel->addcond('LEFT(U01.enrollment_date,6)='.$searchList['enrollment_yearmonth']);
			}
			if ( !empty($searchList['identify_confirm']) ) {
				$sel->addcond('U01.identify_confirm='.$searchList['identify_confirm']);
			}
			if ( !empty($searchList['plan_expiry_date']) ) {
				$sel->addcond('U01.plan_expiry_date>0');
			}
			$sel->addcond('U01.Remove=0');
			$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
			if ( !empty($searchList['media']) ) {
				if ( $searchList['media'] == 999999999 ) {
					$sel->addcond('W07.media=0');
				} else {
					$sel->addcond('W07.media='.$searchList['media']);
				}
			}
			$sel->addcond('W07.Remove=0');
			if ( !empty($searchList['payment']) ) {
				$sel->addinner('P01_payments P01', 'P01.user_id=U01.user_id');
				$sel->addcond('P01.payment_result=1');
				$sel->addcond('P01.Remove=0');
			}
			return $this->loadCalcResult($sel, 'COUNT(distinct(U01.user_id))');
		}
		// ----- Google広告集計 -----
		function U01C10($enrollment_date, $user_sex, $media_id, $campaign_type) {
			$enrollment_date = intval($enrollment_date);
			$user_sex = intval($user_sex);
			$media_id = intval($media_id);
			$campaign_type = intval($campaign_type);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('U01.user_sex='.$user_sex);
			$sel->addcond('U01.enrollment_date='.$enrollment_date);
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			$sel->addinner('W07_browse_media W07', 'W07.user_id=U01.user_id');
			$sel->addcond('W07.media='.$media_id);
			$sel->addcond('W07.Remove=0');
			$sel->addinner('W06_access_media W06', 'W06.access_media_id=W07.access_media_id');
			$sel->addcond('W06.campaign_type='.$campaign_type);
			$sel->addcond('W06.Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		// ----- アクティブユーザー集計 -----
		function U01C11($searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['user_sex']) ) {
				$sel->addcond('U01.user_sex='.$searchList['user_sex']);
			}
			if ( !empty($searchList['last_action_date_time']) ) {
				$sel->addcond('U01.last_action_date_time>='.$searchList['last_action_date_time']);
			}
			$sel->addcond('U01.forced_private=0');
			$sel->addcond('U01.user_status!=10');
			$sel->addcond('U01.dummy_flag=0');
			$sel->addcond('U01.Remove=0');
			if ( !empty($searchList['plan_type']) ) {
				$sel->addinner('M20_plans M20', 'M20.plan_id=U01.plan_id');
				$sel->addcond('M20.plan_type='.$searchList['plan_type']);
				$sel->addcond('M20.Remove=0');
			} else {
				$sel->addcond('U01.plan_id=0');
			}
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		// ----- 承認申請 -----
		function U01C12($date_time, $isNomal=0) {
			$date_time = intval($date_time);
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($date_time) ) {
				$sel->addcond('U01.identify_date_time<='.$date_time);
			}
			$sel->addcond('U01.identify_confirm=2');
			$sel->addcond('U01.forced_private in (0,2)'); //0:公開,2:保留
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.dummy_flag=0');
			if (empty($isNomal)) {
				$sel->addcond('U01.un_scam=0');
			} else {
				$sel->addcond('U01.un_scam in (0,3)'); //0:通常,3:承認保留
			}
			$sel->addcond('U01.Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		// ----- マッチング数集計 -----
		function U01C13($searchList) {
			$sel = $this->CreateSSql(FALSE);
			if ( !empty($searchList['except_self']) && !empty($searchList['user_id']) ) {
				$sel->addcond('U01.user_id!='.$searchList['user_id']);
			}
			if ( !empty($searchList['user_mailaddress']) ) {
				$sel->addcond('U01.user_mailaddress="'.$searchList['user_mailaddress'].'"');
			}
			$sel->addcond('U01.Remove=0');
			$sel->addcond('U01.alert_flag=0'); //0：正常になっていること
			if ( !empty($searchList['is_match']) ) {
				$sel->addinner('U42_summaries U42', 'U42.user_id=U01.user_id');
				$sel->addcond('U42.match_count>0');
				$sel->addcond('U42.receive_message_count>0');
				$sel->addcond('U42.send_message_count>0');
				$sel->addcond('U42.Remove=0');
			}
			return $this->loadCalcResult($sel, 'COUNT(U01.user_id)');
		}
		// ----- 入力チェック -----
		function U01E01($dataU01, $passNeed) {
			InputCheck($dataU01['user_id'], '会員ID', '', 'N', 9, 0);
			InputCheck($dataU01['user_code'], '会員コード', '', 'H', 20, 0);
			InputCheck($dataU01['user_mailaddress'], 'メールアドレス', 'N', 'M', 255, 0);
			$chkU01 = $this->U01G04($dataU01['user_id'], $dataU01['user_mailaddress']);
			if ( $chkU01['user_id'] > 0 ) {
				SetError('登録済みのメールアドレスです。');
			}
			InputCheck($dataU01['user_password'], 'パスワード', ($passNeed)?'N':'', 'H', 128, 0);
			if ( $passNeed ) {
				if ( !preg_match('/\A(?=.*?[a-z])(?=.*?\d)[a-z\d]{8,16}+\z/i',$dataU01['user_password']) ) {
					SetError('パスワードは英小文字、数字を使用して、8文字以上16文字以下で設定してください。');
				}
			}
			InputCheck($dataU01['user_nickname'], 'ニックネーム', 'N', '', 0, 80);
			InputCheck($dataU01['user_sex'], '性別', 'N', 'N', 1,0);
			InputCheck($dataU01['user_birthday'], '生年月日', 'N', 'H', 11, 0);
			if ( !empty($dataU01['user_birthday']) ) {
				$birthYear = date('Y',strtotime($dataU01['user_birthday']));
				$birthMonth = date('m',strtotime($dataU01['user_birthday']));
				$birthDay = date('d',strtotime($dataU01['user_birthday']));
				if ( !checkdate($birthMonth, $birthDay, $birthYear) ) {
					SetError('生年月日が正しくありません。');
				}
				if ( $birthYear < (date('Y') - 90) ) {
					SetError('生年月日、「年」が正しくありません。');
				}
			}
			if ( floor( ( date('Ymd') - date('Ymd',strtotime($dataU01['user_birthday'])) ) / 10000 ) < 18 ) {
				SetError('18歳未満は登録できません。');
			}
			InputCheck($dataU01['user_height'], '身長', '', 'N', 9, 0);
			InputCheck($dataU01['user_blood'], '血液型', '', 'N', 4, 0);
			InputCheck($dataU01['redidence_prefecture_code'], '居住地', 'N', 'N', 2, 0);
			InputCheck($dataU01['birthplace_prefecture_code'], '出身地', '', 'N', 2, 0);
			InputCheck($dataU01['school_career_id'], '学歴', 'N', 'N', 9, 0);
			InputCheck($dataU01['income_id'], '年収', '', 'N', 9, 0);
			InputCheck($dataU01['alcohol_id'], '飲酒', '', 'N', 9, 0);
			InputCheck($dataU01['cigarette_id'], 'たばこ', '', 'N', 9, 0);
			InputCheck($dataU01['child_id'], '趣味', '', 'N', 9, 0);
			InputCheck($dataU01['housemate_id'], '好きなタイプ（見た目）', '', 'N', 9, 0);
			InputCheck($dataU01['family_relationship_id'], '家族との仲', '', 'N', 9, 0);
			InputCheck($dataU01['family_continuation_relationship_id'], '家族関係の継続', '', 'N', 9, 0);
			InputCheck($dataU01['personality_id'], '性格タイプ', '', 'N', 9, 0);
			$one_word = preg_replace("/( |　)/", "", $dataU01['one_word'] );
			InputCheck($one_word, '一言コメント', 'N', '', 0, 80);
			InputCheck($dataU01['self_introduction'], '自己紹介文', '', '', 0, 30000);
			InputCheck($dataU01['about_date_fee'], 'デート代', '', '', 0, 30000);
			InputCheck($dataU01['desired_age'], '希望年齢', '', '', 0, 30000);
			InputCheck($dataU01['identify_confirm'], '本人確認', '', 'N', 1, 0);
			InputCheck($dataU01['identify_date_time'], '本人確認申請時間', '', 'N', 20, 0);
			InputCheck($dataU01['annual_confirm'], '年収確認', '', 'N', 1, 0);
			InputCheck($dataU01['plan_id'], 'プラン', '', 'N', 9, 0);
			InputCheck($dataU01['plan_expiry_date'], 'プラン更新日', '', 'N', 9, 0);
			InputCheck($dataU01['none_public_status'], '非公開', '', 'N', 1, 0);
			InputCheck($dataU01['forced_private'], '強制非公開', '', 'N', 1, 0);
			InputCheck($dataU01['user_status'], '会員ステータス', '', 'N', 2, 0);
			InputCheck($dataU01['enrollment_date'], '入会日', '', 'N', 9, 0);
			InputCheck($dataU01['enrollment_device_type'], '登録デバイス区分', '', 'N', 1, 0);
			InputCheck($dataU01['withdrawal_date'], '退会日', '', 'N', 9, 0);
			InputCheck($dataU01['font_size'], 'フォントサイズ', '', 'N', 1, 0);
			InputCheck($dataU01['dummy_flag'], '会員', '', 'N', 1, 0);
			InputCheck($dataU01['alert_flag'], '要注意', '', 'N', 1, 0);
			InputCheck($dataU01['warning_flag'], '警告', '', 'N', 1, 0);
			InputCheck($dataU01['un_scam'], '詐欺除外', '', 'N', 1, 0);
			InputCheck($dataU01['user_description'], '備考', '', '', 0, 30000);
		}
		// ----- 入力チェック -----
		function U01E02($dataU01, $passNeed) {
			InputCheck($dataU01['user_id'], '会員ID', '', 'N', 9, 0);
			InputCheck($dataU01['user_code'], '会員コード', '', 'H', 20, 0);
			InputCheck($dataU01['user_mailaddress'], 'メールアドレス', 'N', 'M', 255, 0);
			$chkU01 = $this->U01G04($dataU01['user_id'], $dataU01['user_mailaddress']);
			if ( $chkU01['user_id'] > 0 ) {
				SetError('登録済みのメールアドレスです。');
			}
			InputCheck($dataU01['user_password'], 'パスワード', ($passNeed)?'N':'', 'H', 128, 0);
			if ( $passNeed ) {
				if ( !preg_match('/\A(?=.*?[a-z])(?=.*?\d)[a-z\d]{8,16}+\z/i',$dataU01['user_password']) ) {
					SetError('パスワードは英小文字、数字を使用して、8文字以上16文字以下で設定してください。');
				}
			}
			InputCheck($dataU01['user_nickname'], 'ニックネーム', 'N', '', 0, 80);
			InputCheck($dataU01['user_sex'], '性別', 'N', 'N', 1,0);
			InputCheck($dataU01['user_birthday'], '生年月日', 'N', 'H', 11, 0);
			if ( !empty($dataU01['user_birthday']) ) {
				$birthYear = date('Y',strtotime($dataU01['user_birthday']));
				$birthMonth = date('m',strtotime($dataU01['user_birthday']));
				$birthDay = date('d',strtotime($dataU01['user_birthday']));
				if ( !checkdate($birthMonth, $birthDay, $birthYear) ) {
					SetError('生年月日が正しくありません。');
				}
				if ( $birthYear < (date('Y') - 90) ) {
					SetError('生年月日、「年」が正しくありません。');
				}
			}
			if ( floor( ( date('Ymd') - date('Ymd',strtotime($dataU01['user_birthday'])) ) / 10000 ) < 18 ) {
				SetError('18歳未満は登録できません。');
			}
			InputCheck($dataU01['user_height'], '身長', '', 'N', 9, 0);
			InputCheck($dataU01['user_blood'], '血液型', '', 'N', 4, 0);
			InputCheck($dataU01['redidence_prefecture_code'], '居住地', '', 'N', 2, 0);
			InputCheck($dataU01['birthplace_prefecture_code'], '出身地', '', 'N', 2, 0);
			InputCheck($dataU01['school_career_id'], '学歴', '', 'N', 9, 0);
			InputCheck($dataU01['income_id'], '年収', '', 'N', 9, 0);
			InputCheck($dataU01['alcohol_id'], '飲酒', '', 'N', 9, 0);
			InputCheck($dataU01['cigarette_id'], 'たばこ', '', 'N', 9, 0);
			InputCheck($dataU01['child_id'], '趣味', '', 'N', 9, 0);
			InputCheck($dataU01['housemate_id'], '好きなタイプ（見た目）', '', 'N', 9, 0);
			InputCheck($dataU01['family_relationship_id'], '家族との仲', '', 'N', 9, 0);
			InputCheck($dataU01['family_continuation_relationship_id'], '家族関係の継続', '', 'N', 9, 0);
			InputCheck($dataU01['personality_id'], '性格タイプ', '', 'N', 9, 0);
			InputCheck($dataU01['one_word'], '一言コメント', '', '', 0, 80);
			InputCheck($dataU01['self_introduction'], '自己紹介文', '', '', 0, 30000);
			InputCheck($dataU01['about_date_fee'], 'デート代', '', '', 0, 30000);
			InputCheck($dataU01['desired_age'], '希望年齢', '', '', 0, 30000);
			InputCheck($dataU01['identify_confirm'], '本人確認', '', 'N', 1, 0);
			InputCheck($dataU01['identify_date_time'], '本人確認申請時間', '', 'N', 20, 0);
			InputCheck($dataU01['annual_confirm'], '年収確認', '', 'N', 1, 0);
			InputCheck($dataU01['plan_id'], 'プラン', '', 'N', 9, 0);
			InputCheck($dataU01['plan_expiry_date'], 'プラン更新日', '', 'N', 9, 0);
			InputCheck($dataU01['none_public_status'], '非公開', '', 'N', 1, 0);
			InputCheck($dataU01['forced_private'], '強制非公開', '', 'N', 1, 0);
			InputCheck($dataU01['user_status'], '会員ステータス', '', 'N', 2, 0);
			InputCheck($dataU01['enrollment_date'], '入会日', '', 'N', 9, 0);
			InputCheck($dataU01['enrollment_device_type'], '登録デバイス区分', '', 'N', 1, 0);
			InputCheck($dataU01['withdrawal_date'], '退会日', '', 'N', 9, 0);
			InputCheck($dataU01['font_size'], 'フォントサイズ', '', 'N', 1, 0);
			InputCheck($dataU01['dummy_flag'], '会員', '', 'N', 1, 0);
			InputCheck($dataU01['alert_flag'], '要注意', '', 'N', 1, 0);
			InputCheck($dataU01['warning_flag'], '警告', '', 'N', 1, 0);
			InputCheck($dataU01['un_scam'], '詐欺除外', '', 'N', 1, 0);
			InputCheck($dataU01['user_description'], '備考', '', '', 0, 30000);
		}
		// -------------------------------------------------
		// 更新（パスワード暗号化）
		function U01U01($dataU01, $creator) {
			if ( empty($creator) ) exit;
			$dataU01['user_password'] = password_hash($dataU01['user_password'], PASSWORD_DEFAULT);
			$dataU01['user_birthday'] = date('Ymd', strtotime($dataU01['user_birthday']));
			return $this->updateAllField($dataU01, $creator);
		}
		// ----- password_hashさせない（更新時そのまま保存） -----
		function U01U02($dataU01, $creator) {
			if ( empty($creator) ) exit;
			$dataU01['user_birthday'] = date('Ymd', strtotime($dataU01['user_birthday']));
			return $this->updateAllField($dataU01, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function U01R01($user_id, $creator) {
			$user_id = intval($user_id);
			$dataU01 = $this->CreateNewDto();
			$dataU01['user_id'] = $user_id;
			return $this->removeRecord($dataU01, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('user_id',DFNUM,FTREAD|FTID);
			$this->addFieldDef('user_code',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('user_mailaddress',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('user_password',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('user_nickname',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('user_sex',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('user_birthday',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('ocr_birthday',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('user_height',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('user_blood',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('redidence_prefecture_code',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('birthplace_prefecture_code',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('school_career_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('income_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('alcohol_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('cigarette_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('child_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('housemate_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('family_relationship_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('family_continuation_relationship_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('personality_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('one_word',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('self_introduction',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('about_date_fee',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('desired_age',DFSTR,FTREAD|FTUPD);
			$this->addFieldDef('identify_confirm',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('identify_date_time',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('identify_administrator_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('annual_confirm',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('plan_id',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('plan_expiry_date',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('none_public_status',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('forced_private',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('forced_private_date_time',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('user_status',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('enrollment_date',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('enrollment_device_type',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('withdrawal_date',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('font_size',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('dummy_flag',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('alert_flag',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('warning_flag',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('un_scam',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('mail_delivery_flag',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('last_action_date_time',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('last_talk_date_time',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('last_profile_date_time',DFNUM,FTREAD|FTUPD);
			$this->addFieldDef('user_description',DFSTR,FTREAD|FTUPD);
			_daoBase::__construct('U01_users','U01');
			// join U02
			$this->addFieldDef('image_id', DFNUM, 0);
			$this->addFieldDef('image_code', DFSTR, 0);
			$this->addFieldDef('image_extension', DFSTR, 0);
			$this->addFieldDef('is_unpublish', DFNUM, 0);
			$this->addFieldDef('version_id', DFSTR, 0);
			$this->addFieldDef('publish_version_id', DFSTR, 0);
			$this->addFieldDef('thumbnail_version_id', DFSTR, 0);
			$this->addFieldDef('U02_Udate', DFSTR, 0);
			// join U04
			$this->addFieldDef('deny_flag', DFNUM, 0);
			// join U10
			$this->addFieldDef('U10_Idate',DFSTR, 0);
			// join U36
			$this->addFieldDef('flag_value',DFNUM, 0);
			// join M20
			$this->addFieldDef('plan_type',DFNUM, 0);
			$this->addFieldDef('plan_title',DFSTR, 0);
			$this->addFieldDef('term_type',DFNUM, 0);
			$this->addFieldDef('plan_term',DFNUM, 0);
			$this->addFieldDef('plan_price',DFNUM, 0);
			$this->addFieldDef('renewal_plan_id',DFNUM, 0);
			// join W07
			$this->addFieldDef('media',DFNUM, 0);
			// join U51
			$this->addFieldDef('scam_value',DFNUM, 0);
			$this->addFieldDef('scam_judge',DFNUM, 0);
			$this->addFieldDef('scam_comment',DFSTR, 0);
			// join U59
			$this->addFieldDef('nickname',DFSTR, 0);
			// join P06
			$this->addFieldDef('paidy_token', DFSTR, 0);
			// join P07
			$this->addFieldDef('paypal_token', DFSTR, 0);
		}
	}
	$dbU01 = new U01_users();
