<?php
	ini_set('error_log', '/var/log/batch.log');
	include("_filepath.inf");
	include_once($gINC.'_global.inc');
	include_once($gLIB.'_pubfunc.inc');
	include_once($gINC.'_healmatefunc.inc');
	include_once($gINC.'logic/openai.inc');
	include_once($gINC.'dao/B06.inc');
	include_once($gINC.'dao/B07.inc');
	include_once($gINC.'dao/B09.inc');
	include_once($gINC.'dao/B14.inc');
	include_once($gINC.'dao/M47.inc');
error_log(basename(__FILE__).' start');
	//----------------------------------------------------------
	// php -q /var/www/bat/batNotificationTags.php
	// 過去の問合せにタグを再設定
	//----------------------------------------------------------

	$max = $argv[1];
	if (empty($max)) {
		$max = 3;
	}

	$searchList = array();

	
	$searchList['exclude_auto'] = 1; //1:自動応答は除く
	$searchList['is_no_comment'] = 1; //1:返信のみ
	$searchList['not_administrator_id'] = 1; //1:管理者からの送信は除く

	//TODO：過去分のタグ設定完了するまで
	// $searchList['is_reply'] = 0; //未対応
	// $searchList['page'] = 1;

	$searchList['limit'] = 10;


	$listM47 = $dbM47->M47S01();

	for ( $i = 0 ; $i < $max ; $i ++ ) {
error_log(basename(__FILE__).' $i='.$i);
		$searchList['page'] = $i;

		$listB06 = $dbB06->B06S02($searchList);
error_log(basename(__FILE__).' cnt='.count($listB06));

		foreach ( $listB06 as $dataB06 ) {

			// if (!empty($dataB06['is_reply'])) {
			// 	//未対応以外はスキップ
			// 	error_log(basename(__FILE__).__LINE__.'|is_reply skip');
			// 	continue;
			// }

			$chkB14 = $dbB14->B14C02($dataB06['notification_id']);
			if ( !empty($chkB14) ) {
				//すでにタグ設定あればスキップ
				error_log(basename(__FILE__).__LINE__.'|B14 skip');
				continue;
			}

			$notification_text = mbTrim($dataB06['notification_text']);
			if ( empty($notification_text) ) {
				//質問なければスキップ
				error_log(basename(__FILE__).__LINE__.'|no question skip');
				continue;
			}

			//タグ検索
			$question = $dataB06['notification_title']."\n".$dataB06['notification_text'];
			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($question));
			foreach( $listM47 as $dataM47 ) {
				if ( strpos($question, $dataM47['notification_tag_name'])  !== false ) {
					$dataB14 = $dbB14->CreateNewDto();
					$dataB14['notification_id'] = $dataB06['notification_id'];
					$dataB14['notification_tag_id'] = $dataM47['notification_tag_id'];
					$dbB14->B14U01($dataB14, basename(__FILE__, ".php"));
				}
			}

		}
	}	
error_log(basename(__FILE__).' finish');
	exit();
