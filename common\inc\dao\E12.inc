<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : E12_schedules
	class E12_schedules extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function E12G01($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			return $this->loadRecord($sel);
		}
		// ----- codeで1件 -----
		function E12G02($schedule_code) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_code="'.$schedule_code.'"');
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function E12S01() {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- 入力チェック -----
		function E12E01($dataE12) {
			InputCheck($dataE12['schedule_id'], 'スケジュールID', 'N', 'N', 9, 0);
			InputCheck($dataE12['schedule_code'], 'スケジュールコード', '', 'H', 20, 0);
			InputCheck($dataE12['user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataE12['schedule_title'], 'スケジュール名', 'N', '', 0, 80);
			InputCheck($dataE12['schedule_text'], '説明文', '', '', 0, 30000);
			InputCheck($dataE12['schedule_choice'], '選択肢', 'N', 'N', 4, 0);
			InputCheck($dataE12['apply_limit'], '回答制限数', '', 'N', 9, 0);
			InputCheck(str_replace('-', '', $dataE12['schedule_close_date']), '締切日', '', 'N', 9, 0);
			InputCheck(str_replace(':', '', $dataE12['schedule_close_time']), '締切時間', '', 'N', 9, 0);
			if ( empty($dataE12['schedule_close_date']) && !empty($dataE12['schedule_close_time']) ) {
				SetError('締切日を登録してください。');
			}
			InputCheck($dataE12['target_menu'], '特定メニュー', '', 'N', 9, 0);
			InputCheck($dataE12['target_menu_id'], '特定メニューID', '', 'N', 9, 0);
			InputCheck($dataE12['is_limit_public'], '限定公開', '', 'N', 1, 0);
		}
		// -------------------------------------------------
		// 更新
		function E12U01($dataE12, $creator) {
			if ( !empty($dataE12['schedule_close_date']) ) {
				$dataE12['schedule_close_date'] = date('Ymd', strtotime($dataE12['schedule_close_date']));
			}
			if ( !empty($dataE12['schedule_close_time']) ) {
				$dataE12['schedule_close_time'] = date('Hi', strtotime($dataE12['schedule_close_time']));
			}
			return $this->updateAutoIncrementAllField($dataE12, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function E12R01($schedule_id, $creator) {
			$schedule_id = intval($schedule_id);
			$dataE12 = $this->CreateNewDto();
			$dataE12['schedule_id'] = $schedule_id;
			return $this->removeRecord($dataE12, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('schedule_id', DFNUM, FTREAD|FTID);
			$this->addFieldDef('schedule_code', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('schedule_title', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('schedule_text', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('schedule_choice', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('apply_limit', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('schedule_close_date', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('schedule_close_time', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('target_menu', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('target_menu_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_limit_public', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('E12_schedules','E12');
		}
	}
	$dbE12 = new E12_schedules();
