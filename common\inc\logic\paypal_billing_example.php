<?php
/**
 * PayPal Billing Agreement Example Usage
 * 
 * This file demonstrates how to use the new PayPal billing agreement functions
 * for recurring payments with HealMate plans.
 */

include_once $gLIB_PATH . 'vendor/autoload.php';
include_once $gINC_PATH . 'logic/paypal.inc';
include_once $gINC_PATH . 'dao/M20.inc';

/**
 * Example 1: Create a billing plan from M20 plan data
 */
function createBillingPlanExample($planId) {
    global $dbM20;
    
    // Get plan data from M20 table
    $planData = $dbM20->M20G01($planId);
    
    if (!$planData) {
        echo "Plan not found: $planId\n";
        return false;
    }
    
    // Create PayPal billing plan
    $result = paypalCreateBillingPlan($planData);
    
    if ($result['success']) {
        echo "Billing plan created successfully!\n";
        echo "PayPal Plan ID: " . $result['plan_id'] . "\n";
        return $result['plan_id'];
    } else {
        echo "Error creating billing plan: " . $result['error'] . "\n";
        return false;
    }
}

/**
 * Example 2: Create a billing agreement from the plan
 */
function createBillingAgreementExample($paypalPlanId, $userEmail, $userName) {
    $params = [
        'plan_id' => $paypalPlanId,
        'subscriber_name' => $userName,
        'subscriber_email' => $userEmail,
        'return_url' => 'https://my.healmate.jp/payment/success',
        'cancel_url' => 'https://my.healmate.jp/payment/cancel'
    ];
    
    $result = paypalCreateBillingAgreement($params);
    
    if ($result['success']) {
        echo "Billing agreement created successfully!\n";
        echo "Subscription ID: " . $result['subscription_id'] . "\n";
        echo "Approval URL: " . $result['approval_url'] . "\n";
        echo "Please redirect user to approval URL for consent.\n";
        return $result;
    } else {
        echo "Error creating billing agreement: " . $result['error'] . "\n";
        return false;
    }
}

/**
 * Example 3: Execute billing agreement after user approval
 */
function executeBillingAgreementExample($subscriptionId) {
    $params = [
        'subscription_id' => $subscriptionId
    ];
    
    $result = paypalExecuteBillingAgreement($params);
    
    if ($result['success']) {
        echo "Billing agreement executed successfully!\n";
        echo "Billing Agreement ID: " . $result['billing_agreement_id'] . "\n";
        echo "Status: " . $result['status'] . "\n";
        return $result['billing_agreement_id'];
    } else {
        echo "Error executing billing agreement: " . $result['error'] . "\n";
        return false;
    }
}

/**
 * Example 4: Charge using existing billing agreement (for recurring payments)
 */
function chargeWithBillingAgreementExample($billingAgreementId, $amount) {
    $params = [
        'billing_agreement_id' => $billingAgreementId,
        'amount' => $amount,
        'currency' => 'JPY'
    ];
    
    $result = paypalChargeWithBillingAgreement($params);
    
    if ($result['success']) {
        echo "Recurring payment charged successfully!\n";
        echo "Order ID: " . $result['order_id'] . "\n";
        return $result;
    } else {
        echo "Error charging recurring payment: " . $result['error'] . "\n";
        return false;
    }
}

/**
 * Complete workflow example
 */
function completeWorkflowExample() {
    echo "=== PayPal Billing Agreement Complete Workflow ===\n\n";
    
    // Step 1: Create billing plan from M20 plan
    echo "Step 1: Creating billing plan...\n";
    $planId = 1; // Example plan ID from M20 table
    $paypalPlanId = createBillingPlanExample($planId);
    
    if (!$paypalPlanId) {
        return false;
    }
    
    echo "\n";
    
    // Step 2: Create billing agreement
    echo "Step 2: Creating billing agreement...\n";
    $userEmail = "<EMAIL>";
    $userName = "Test Customer";
    $agreementResult = createBillingAgreementExample($paypalPlanId, $userEmail, $userName);
    
    if (!$agreementResult) {
        return false;
    }
    
    echo "\n";
    echo "*** User needs to approve the agreement at: " . $agreementResult['approval_url'] . " ***\n";
    echo "*** After approval, continue with Step 3 ***\n\n";
    
    // Step 3: Execute billing agreement (after user approval)
    // This would typically be called from a webhook or return URL handler
    echo "Step 3: Executing billing agreement (after user approval)...\n";
    $subscriptionId = $agreementResult['subscription_id'];
    $billingAgreementId = executeBillingAgreementExample($subscriptionId);
    
    if (!$billingAgreementId) {
        return false;
    }
    
    echo "\n";
    
    // Step 4: Use billing agreement for recurring payments
    echo "Step 4: Charging recurring payment...\n";
    $amount = "1000"; // Example amount in JPY
    $chargeResult = chargeWithBillingAgreementExample($billingAgreementId, $amount);
    
    if ($chargeResult) {
        echo "\n=== Workflow completed successfully! ===\n";
        return true;
    }
    
    return false;
}

// Uncomment to run the example
// completeWorkflowExample();

?>
