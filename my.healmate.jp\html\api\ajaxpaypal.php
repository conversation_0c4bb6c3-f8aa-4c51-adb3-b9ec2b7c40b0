<?php
	include_once('_global.inc');
	include_once('_smarty.inc');
	include_once('logic/paypal.inc');
	include_once('logic/login.inc');
	include_once('logic/mail.inc');
	include_once('_mmcrypt.inc');
	include_once('dao/U01.inc');
	include_once('dao/P07.inc');

	$action = prv('action');
	$order_id = prv('order_id');
	$user_id = prh('user_id');
	$dbUser = prh('dbuser');
	$amount = prv('amount');
	$return_url = prv('return_url');
	$cancel_url = prv('cancel_url');

	if ( empty($action) || empty($dbUser) ) {
		SetError('パラメータが正しくありません。');
	}

	$err = '';
	$result = 1;
	if ( !empty($gERROR) ) {
		$result = 9;
		$err = implode('/', $gERROR);
	} else {
        // Paypal payment process
		if ($action == 'create') {
			$params = [
				'currency' => 'JPY',
				'amount' => $amount,
				'return_url' => $return_url,
				'cancel_url' => $cancel_url,
			];

			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($params));

			$paymentData = paypalCreateOrder($params);
			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($paymentData));

		} else if ($action == 'authorize') {
			// Not yet implemented
		} else if ($action == 'capture') {

			$params = [
				'order_id' => $order_id,
			];

			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($params));

			$paymentData = capturePaypalOrder($params);
			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($paymentData));

			// $dataP07 = $dbP07->CreateNewDto();
			// $dataP07['user_id'] = $_SESSION['user_id'];
			// $dataP07['paypal_token'] = ['account_id'];
			// $dataP07 = $dbP07->P07U01($dataP07, $dbUser);

			
			$dataP07 = $dbP07->CreateNewDto();
			$dataP07['user_id'] = $user_id;
			$dataP07['paypal_token'] = $paymentData['payment_source']['paypal']['account_id'];
			// $dataP07['status'] = $paymentData['status'];
			$dataP07['paypal_order_id'] = $paymentData['id'];
			// $dataP07['vault_status'] = $paymentData['payment_source']['paypal']['attributes']['vault']['status'];
			// $dataP07['capture_id'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['id'];
			// $dataP07['capture_status'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['status'];
			// $dataP07['amount_value'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['amount']['value'];
			// $dataP07['amount_currency'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['amount']['currency_code'];
			// $dataP07['paypal_fee'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['seller_receivable_breakdown']['paypal_fee']['value'];
			// $dataP07['net_amount'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['seller_receivable_breakdown']['net_amount']['value'];
			// $dataP07['create_time'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['create_time'];
			// $dataP07['update_time'] = $paymentData['purchase_units'][0]['payments']['captures'][0]['update_time'];
			// $dataP07['payer_id'] = $paymentData['payer']['payer_id'];
			// $dataP07['payer_country'] = $paymentData['payer']['address']['country_code'];

			error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($dataP07));

			$dbP07->P07U01($dataP07, $user_id);


		}
	}

	$data = array("result" => $result, "error" => $err, "data" => $paymentData);
	header("Content-type: application/json; charset=UTF-8");
	echo json_encode($data);
	exit;
