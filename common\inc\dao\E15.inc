<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : E15_schedule_comments
	class E15_schedule_comments extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function E15G01($schedule_id, $user_id) {
			$schedule_id = intval($schedule_id);
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('user_id='.$user_id);
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function E15S01($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addcond('E15.schedule_id='.$schedule_id);
			$sel->addcond('E15.Remove=0');
			$sel->addinner('U01_users U01', 'U01.user_id=E15.user_id');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=E15.user_id and U02.image_id = 1 and U02.Remove=0');
			return $this->loadList($sel);
		}
		// ----- 入力チェック -----
		function E15E01($dataE15) {
			InputCheck($dataE15['schedule_id'], 'スケジュールID', 'N', 'N', 9, 0);
			InputCheck($dataE15['user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataE15['schedule_comment'], 'コメント', '', '', 0, 680);
		}
		// -------------------------------------------------
		// 更新
		function E15U01($dataE15, $creator) {
			return $this->updateAllField($dataE15, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function E15R01($schedule_id, $user_id, $creator) {
			$schedule_id = intval($schedule_id);
			$user_id = intval($user_id);
			$dataE15 = $this->CreateNewDto();
			$dataE15['schedule_id'] = $schedule_id;
			$dataE15['user_id'] = $user_id;
			return $this->removeRecord($dataE15, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('schedule_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('schedule_comment', DFSTR, FTREAD|FTUPD);
			_daoBase::__construct('E15_schedule_comments','E15');
			// join U01
			$this->addFieldDef('user_code', DFSTR, 0);
			$this->addFieldDef('user_nickname', DFSTR, 0);
			$this->addFieldDef('user_sex', DFNUM, 0);
			// join U02
			$this->addFieldDef('image_id', DFNUM, 0);
			$this->addFieldDef('image_code', DFSTR, 0);
			$this->addFieldDef('image_extension', DFSTR, 0);
			$this->addFieldDef('version_id', DFSTR, 0);
			$this->addFieldDef('publish_version_id', DFSTR, 0);
			$this->addFieldDef('thumbnail_version_id', DFSTR, 0);
		}
	}
	$dbE15 = new E15_schedule_comments();
