<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : E13_schedule_candidates
	class E13_schedule_candidates extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function E13G01($schedule_id, $schedule_candidate_id) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('schedule_candidate_id='.$schedule_candidate_id);
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function E13S01($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('schedule_id='.$schedule_id);
			$sel->addcond('Remove=0');
			$sel->addorder('STR_TO_DATE(CONCAT(candidate_date, LPAD(candidate_time, 4, "0")), "%Y%m%d%H%i") ASC');
			return $this->loadList($sel);
		}
		// ----- 入力チェック -----
		function E13E01($dataE13) {
			InputCheck($dataE13['schedule_id'], 'スケジュールID', '', 'N', 9, 0);
			InputCheck($dataE13['schedule_candidate_id'], '候補日ID', '', 'N', 9, 0);
			InputCheck(str_replace('-', '', $dataE13['candidate_date']), '候補日', 'N', 'N', 9, 0);
			InputCheck(str_replace(':', '', $dataE13['candidate_time']), '候補時間', 'N', 'N', 9, 0);
		}
		// -------------------------------------------------
		// 更新
		function E13U01($dataE13, $creator) {
			if ( !empty($dataE13['candidate_date']) ) {
				$dataE13['candidate_date'] = date('Ymd', strtotime($dataE13['candidate_date']));
			}
			if ( !empty($dataE13['candidate_time']) ) {
				$dataE13['candidate_time'] = date('Hi', strtotime($dataE13['candidate_time']));
			}
			return $this->updateAllField($dataE13, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function E13R01($schedule_id, $schedule_candidate_id, $creator) {
			$schedule_id = intval($schedule_id);
			$schedule_candidate_id = intval($schedule_candidate_id);
			$dataE13 = $this->CreateNewDto();
			$dataE13['schedule_id'] = $schedule_id;
			$dataE13['schedule_candidate_id'] = $schedule_candidate_id;
			return $this->removeRecord($dataE13, $creator);
		}
		// 物理削除
		function E13R02($schedule_id) {
			$schedule_id = intval($schedule_id);
			$sql = new query;
			$sql->addsql('delete from E13_schedule_candidates where schedule_id='.$schedule_id);
			$mainDB = MainDB();
			$sql->exec($mainDB);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('schedule_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('schedule_candidate_id', DFNUM, FTREAD|FTID);
			$this->addFieldDef('candidate_date', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('candidate_time', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('E13_schedule_candidates','E13');
		}
	}
	$dbE13 = new E13_schedule_candidates();
