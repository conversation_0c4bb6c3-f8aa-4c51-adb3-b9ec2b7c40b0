<?php
include_once $gLIB_PATH . 'vendor/autoload.php';

use PaypalServerSdkLib\PaypalServerSdkClient;
use PaypalServerSdkLib\PaypalServerSdkClientBuilder;
use PaypalServerSdkLib\Authentication\ClientCredentialsAuthCredentialsBuilder;
use PaypalServerSdkLib\Logging\LoggingConfigurationBuilder;
use PaypalServerSdkLib\Logging\RequestLoggingConfigurationBuilder;
use PaypalServerSdkLib\Logging\ResponseLoggingConfigurationBuilder;
use PaypalServerSdkLib\Models\Builders\OrderRequestBuilder;
use PaypalServerSdkLib\Models\CheckoutPaymentIntent;
use PaypalServerSdkLib\Models\Builders\PurchaseUnitRequestBuilder;
use PaypalServerSdkLib\Models\Builders\AmountWithBreakdownBuilder;
use PaypalServerSdkLib\Environment;
use PaypalServerSdkLib\Controllers\OrdersController;
use Psr\Log\LogLevel;

// Init paypal client
function paypalInit() {
    try {
        global $gPAYPALClientID, $gPAYPALSecret;

        $client = PaypalServerSdkClientBuilder::init()
        ->clientCredentialsAuthCredentials(
            ClientCredentialsAuthCredentialsBuilder::init(
                $gPAYPALClientID,
                $gPAYPALSecret
            )
        )
        ->environment(Environment::SANDBOX)
        //->loggingConfiguration(
        //    LoggingConfigurationBuilder::init()
        //        ->level(LogLevel::INFO)
        //        ->requestConfiguration(RequestLoggingConfigurationBuilder::init()->body(true))
        //        ->responseConfiguration(ResponseLoggingConfigurationBuilder::init()->headers(true))
        //)
        ->build();

        return $client;
    } catch (Exception $e) {
        error_log("PayPal initialization error: " . $e->getMessage());
        throw new Exception("Failed to initialize PayPal client");
    }
}


// Create paypal order
// function paypalCreateOrder($params) {
//     $amount = $params['amount'] ?? null;
//     $currency = $params['currency'] ?? 'JPY';

//     // validate amount and currency
//     if (empty($amount) || empty($currency)) {
//         throw new Exception("Amount and currency are required");
//     }

//     $client = paypalInit();
//     $ordersController = $client->getOrdersController();

//     $collect = [
//         'body' => OrderRequestBuilder::init(
//             CheckoutPaymentIntent::CAPTURE,
//             [
//                 PurchaseUnitRequestBuilder::init(
//                     AmountWithBreakdownBuilder::init(
//                         $currency,
//                         $amount
//                     )->build()
//                 )->build()
//             ]
//         )->build(),
//         'prefer' => 'return=minimal'
//     ];

//     $apiResponse = $ordersController->createOrder($collect);
//     if ($apiResponse->isSuccess()) {
//         $result = $apiResponse->getResult();
//     } else {
//         $result = $apiResponse->getBody();
//     }

//     return $result;
// }


// Authorize paypal order
function paypalAuthorizeOrder($params) {
    // Not yet implemented
    return null;
}

// Capture paypal order
function paypalCaptureOrder($params) {
    $order_id = $params['order_id'];

    // validate order id
    if (empty($order_id)) {
        throw new Exception("Order ID is required");
    }

    $client = paypalInit();
    $ordersController = $client->getOrdersController();

    $collect = [
        'id' => $order_id,
        'prefer' => 'return=minimal'
    ];

    $apiResponse = $ordersController->captureOrder($collect);
    if ($apiResponse->isSuccess()) {
        $result = $apiResponse->getResult();
    } else {
        $result = $apiResponse->getBody();
    }

    return $result;
    
}

// Get paypal order
function paypalGetOrder($params) {
    $order_id = $params['order_id'];

    // validate order id
    if (empty($order_id)) {
        throw new Exception("Order ID is required");
    }

    $client = paypalInit();
    $ordersController = $client->getOrdersController();

    $collect = [
        'id' => $order_id,
        'prefer' => 'return=minimal'
    ];

    $apiResponse = $ordersController->getOrder($collect);
    if ($apiResponse->isSuccess()) {
        $result = $apiResponse->getResult();
    } else {
        $result = $apiResponse->getBody();
    }

    return $result;
}



function getPaypalAccessToken() {
    global $gPAYPALClientID, $gPAYPALSecret, $gPAYPAL_URL;

    $url = $gPAYPAL_URL . "v1/oauth2/token";
    $headers = ["Accept: application/json"];
    $postFields = "grant_type=client_credentials";

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_USERPWD, $gPAYPALClientID . ":" . $gPAYPALSecret);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);
    if (isset($result['access_token'])) {
        return $result['access_token'];
    }
    throw new Exception("Failed to get access token: " . $response);
}

function paypalCreateOrder($params, $saveCard = true) {
    global $gPAYPAL_URL;

    $accessToken = getPaypalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders";
    $currency = $params['currency'] ?? 'JPY';

    // vault設定
    $vaultSettings = [
        "store_in_vault" => $saveCard ? "ON_SUCCESS" : "OFF"
    ];

    $body = [
        "intent" => "CAPTURE",
        "payment_source" => [
            "card" => [
                "attributes" => [
                    "vault" => $vaultSettings,
                    "verification" => [
                        "method" => "SCA_ALWAYS"
                    ]
                ],
                "experience_context" => [
                    "return_url" => $params['return_url'],
                    "cancel_url" => $params['cancel_url'],
                    "shipping_preference" => "NO_SHIPPING"
                ]
            ]
        ],
        "purchase_units" => [[
            "amount" => [
                "currency_code" => $currency,
                "value" => $params['amount']
            ]
        ]]
    ];

    // 既存顧客IDがあれば追加（上書き保存用）
    if (!empty($params['paypal_customer_id'])) {
        $body["customer"] = [
            "id" => $params['paypal_customer_id']
        ];
    }

    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}"
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);
    $result = json_decode($response, true);


      error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($result));


    return $result;
}


function capturePaypalOrder($params) {
    global $gPAYPAL_URL;

    $accessToken = getPaypalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders/{$params['order_id']}/capture";

    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($url));

    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}"
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);
    return $result;
}


function paypalChargeWithVaultId($params) {
    global $gPAYPALClientID, $gPAYPALSecret, $gPAYPAL_URL;

    $accessToken = getPayPalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders";
    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}",
    ];

    $body = [
        "intent" => "CAPTURE",
        "payment_source" => [
            "token" => [
                "id" => $params['vault_id'],
                "type" => "BILLING_AGREEMENT"
            ]
        ],
        "purchase_units" => [[
            "amount" => [
                "currency_code" => $params['currency'],
                "value" => $params['amount']
            ]
        ]]
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($body));
    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($response));


    $result = json_decode($response, true);
    return $result;
}

// Create PayPal billing plan for recurring payments
function paypalCreateBillingPlan($planData) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $url = $gPAYPAL_URL . "v1/billing-plans";

        // Build plan name based on plan details
        $planName = $planData['plan_title'];
        if ($planData['term_type'] == 1) {
            $planName .= ' ' . $planData['plan_term'] . '日間';
            $frequency = 'Day';
            $frequencyInterval = $planData['plan_term'];
        } else {
            $planName .= ' ' . $planData['plan_term'] . 'ヶ月';
            $frequency = 'Month';
            $frequencyInterval = $planData['plan_term'];
        }

        $body = [
            "name" => $planName,
            "description" => $planData['plan_description'] ?? $planName,
            "type" => "INFINITE",
            "payment_definitions" => [
                [
                    "name" => "Regular payment definition",
                    "type" => "REGULAR",
                    "frequency" => $frequency,
                    "frequency_interval" => $frequencyInterval,
                    "amount" => [
                        "value" => $planData['plan_price'],
                        "currency" => "JPY"
                    ],
                    "cycles" => "0" // 0 means infinite cycles
                ]
            ],
            "merchant_preferences" => [
                "auto_bill_amount" => "yes",
                "cancel_url" => "https://my.healmate.jp/payment/cancel",
                "return_url" => "https://my.healmate.jp/payment/success",
                "initial_fail_amount_action" => "continue",
                "max_fail_attempts" => "3",
                "setup_fee" => [
                    "value" => "0",
                    "currency" => "JPY"
                ]
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingPlan request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingPlan response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            // Activate the plan
            $planId = $result['id'];
            $activateResult = paypalActivateBillingPlan($planId);

            if ($activateResult['success']) {
                return [
                    'success' => true,
                    'plan_id' => $planId,
                    'data' => $result
                ];
            } else {
                return $activateResult;
            }
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing plan creation error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Activate PayPal billing plan
function paypalActivateBillingPlan($planId) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $url = $gPAYPAL_URL . "v1/billing-plans/{$planId}";

        $body = [
            [
                "op" => "replace",
                "path" => "/",
                "value" => [
                    "state" => "ACTIVE"
                ]
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalActivateBillingPlan request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalActivateBillingPlan response: '.var_dump_text($response));

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'plan_id' => $planId,
                'status' => 'ACTIVE'
            ];
        } else {
            $result = json_decode($response, true);
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Failed to activate plan',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing plan activation error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Create PayPal billing agreement from billing plan
function paypalCreateBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $url = $gPAYPAL_URL . "v1/billing-agreements";

        // Required parameters
        $planId = $params['plan_id'];
        $subscriberName = $params['subscriber_name'] ?? '';
        $subscriberEmail = $params['subscriber_email'] ?? '';
        $returnUrl = $params['return_url'] ?? '';
        $cancelUrl = $params['cancel_url'] ?? '';

        // Validate required parameters
        if (empty($planId)) {
            throw new Exception("Plan ID is required");
        }

        $body = [
            "name" => "HealMate Billing Agreement",
            "description" => "Recurring payment agreement for HealMate services",
            "start_date" => date('c', strtotime('+1 minute')), // Start 1 minute from now
            "plan" => [
                "id" => $planId
            ],
            "payer" => [
                "payment_method" => "paypal"
            ],
            "override_merchant_preferences" => [
                "return_url" => $returnUrl,
                "cancel_url" => $cancelUrl,
                "auto_bill_amount" => "yes",
                "initial_fail_amount_action" => "continue",
                "max_fail_attempts" => "3"
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingAgreement request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingAgreement response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            // Find approval URL from links
            $approvalUrl = '';
            if (isset($result['links'])) {
                foreach ($result['links'] as $link) {
                    if ($link['rel'] === 'approval_url') {
                        $approvalUrl = $link['href'];
                        break;
                    }
                }
            }

            return [
                'success' => true,
                'agreement_token' => $result['token'] ?? '',
                'approval_url' => $approvalUrl,
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement creation error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Execute/Activate PayPal billing agreement after customer approval
function paypalExecuteBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $paymentToken = $params['payment_token'] ?? $params['token'];

        // Validate required parameters
        if (empty($paymentToken)) {
            throw new Exception("Payment token is required");
        }

        // Execute billing agreement using v1/billing-agreements API
        $url = $gPAYPAL_URL . "v1/billing-agreements/{$paymentToken}/agreement-execute";

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        // Empty body for agreement execution
        $body = [];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalExecuteBillingAgreement request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalExecuteBillingAgreement response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'billing_agreement_id' => $result['id'],
                'status' => $result['state'],
                'payer_id' => $result['payer']['payer_info']['payer_id'] ?? '',
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement execution error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Charge using existing billing agreement for recurring payments
function paypalChargeWithBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $billingAgreementId = $params['billing_agreement_id'];
        $amount = $params['amount'];
        $currency = $params['currency'] ?? 'JPY';
        $note = $params['note'] ?? 'HealMate recurring payment';

        // Validate required parameters
        if (empty($billingAgreementId) || empty($amount)) {
            throw new Exception("Billing agreement ID and amount are required");
        }

        // Execute billing agreement payment using v1/billing-agreements API
        $url = $gPAYPAL_URL . "v1/billing-agreements/{$billingAgreementId}/bill-balance";

        $body = [
            "note" => $note,
            "amount" => [
                "currency" => $currency,
                "value" => $amount
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalChargeWithBillingAgreement request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalChargeWithBillingAgreement response: '.var_dump_text($response));

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'billing_agreement_id' => $billingAgreementId,
                'amount' => $amount,
                'currency' => $currency,
                'response' => $response
            ];
        } else {
            $result = json_decode($response, true);
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement charge error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

