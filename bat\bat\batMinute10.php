<?php
	ini_set('error_log', '/var/log/batch.log');
	include("_filepath.inf");
	include_once($gINC.'_global.inc');
	include_once($gLIB.'_pubfunc.inc');
	include_once($gINC.'dao/A12.inc');
	include_once($gINC.'dao/G10.inc');
	include_once($gINC.'dao/W39.inc');
	include_once($gINC.'dao/W40.inc');
error_log('batMinute10 start');
	// 10分ごとに起動
	// ----- G01終了日更新 -----
	$listG10 = $dbG10->G10S03();
	foreach ( $listG10 as $dataG10 ) {
		if ( $dataG10['schedule_close_date'] < date('Ymd') || date('YmdHi', strtotime($dataG10['schedule_close_date'] . ' ' . $dataG10['schedule_close_time'])) < date('YmdHi') ) {
			$dataG10['is_end'] = 1;
			$dbG10->G10U01($dataG10, basename(__FILE__, ".php"));
		}
	}
	
	// ----- W39削除 -----
	$before10minute = date('Y-m-d H:i:s', mktime(date('H'), date('i') - 10, date('s'), date('m'), date('d'), date('Y')));
error_log('before10minute:'.$before10minute);
	$listW39 = $dbW39->W39S01($before10minute);
error_log('cntW39:'.COUNT($listW39));
	foreach ( $listW39 as $dataW39 ) {
		$dbW39->W39R02($dataW39['black_segment_id']);
	}
/*
	// ----- W40によるW39自動稼働停止 -----
	$dataW40 = $dbW40->W40G01(1);
	if ( $dataW40['count'] >= 5 ) {
		// ----- W40、0にする -----
		$dataW40['count'] = 0;
		$dbW40->W40U01($dataW40, basename(__FILE__, ".php"));
	}
*/
/*
	// ----- A12:OFF ------
	$dataA1201 = $dbA12->A12G01(1);
	if ( $dataA1201['is_operation'] ) {
		$dataA1201['is_operation'] = 0;
		$dbA12->A12U01($dataA1201, basename(__FILE__, ".php"));
	}
	// ----- A12:OFF ------
	$dataA1202 = $dbA12->A12G01(2);
	if ( $dataA1202['is_operation'] ) {
		$dataA1202['is_operation'] = 0;
		$dbA12->A12U01($dataA1202, basename(__FILE__, ".php"));
	}
	// ----- A12:OFF ------
	$dataA1203 = $dbA12->A12G01(3);
	if ( $dataA1203['is_operation'] ) {
		$dataA1203['is_operation'] = 0;
		$dbA12->A12U01($dataA1203, basename(__FILE__, ".php"));
	}
*/
error_log('batMinute10 finish');
	exit();
