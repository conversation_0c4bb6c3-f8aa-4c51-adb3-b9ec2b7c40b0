<?php
  include_once('_daobase.inc');
  // -----------------------------------------------------
  // Table : M28_reply_mail_templates
  class M28_reply_mail_templates extends _daoBase {
    // -------------------------------------------------
    // 検索
    // ----- PKで1件 -----
    function M28G01($template_id) {
      $template_id = intval($template_id);
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('template_id='.$template_id);
      return $this->loadRecord($sel);
    }
    // ----- リスト -----
    function M28S01() {
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('Remove=0');
      $sel->addorder('turn');
      return $this->loadList($sel);
    }
    // ----- リスト（表示リストのみ） -----
    function M28S02($searchList=[]) {
      $sel = $this->CreateSSql(TRUE);
			$this->addcondition($sel, $searchList);
      $sel->addleftjoin('M34_notification_categories M34', 'M28.notification_category_id = M34.notification_category_id');
      $sel->addfield('M34.notification_category_name');
      if ( !empty($searchList['limit']) ) {
        $sel->setoffset(($searchList['page'] - 1) * $searchList['limit']);
        $sel->setlimit($searchList['limit']);
      }
      $sel->addorder('M34.turn, M28.turn');
      return $this->loadList($sel);
    }
    function M28C01($searchList=[]) {
      $sel = $this->CreateSSql(FALSE);
			$this->addcondition($sel, $searchList);
      return $this->loadCalcResult($sel, 'COUNT(M28.template_id)');
    }
		// ----- 抽出条件 -----
		private function addcondition(&$sel, $searchList) {
      $sel->addcond('M28.hide_flag=0');
      $sel->addcond('M28.Remove=0');
			if ( !empty($searchList['notification_category_id']) ) $sel->addcond('M28.notification_category_id='.$searchList['notification_category_id']);
      if ( !empty($searchList['category_id']) ) {
        $sel->addcond('M28.notification_category_id ='.$searchList['category_id']);
      }
      $condkeyword = '';
      if ( !empty($searchList['notification_tag_name']) ) {
        foreach ($searchList['notification_tag_name'] as $tag) {
          $tag = trim($tag);
          if (!empty($tag)) {
            if (!empty($condkeyword )) $condkeyword .= ' and ';
            $condkeyword .= "(concat(IFNULL(M28.template_title, ''), IFNULL(M28.question, ''), IFNULL(M28.template, ''))";
            $condkeyword .= '"%'.$tag.'%")';
          }
        }
        if (!empty($condkeyword)) $sel->addcond($condkeyword);
      }
      if ( !empty($searchList['keyword']) ) {
        //キーワード検索（スペースで分割して、and検索）
        $condkeyword = seach_split("concat(IFNULL(M28.template_title, ''), IFNULL(M28.question, ''), IFNULL(M28.template, ''))", $searchList['keyword'], 'and');
        if (!empty($condkeyword)) $sel->addcond($condkeyword);
      }
      if ( !empty($searchList['tag_ids']) ) {
  			$sel->addinner('M48_reply_mail_template_tags M48','M48.template_id=M28.template_id');
        $sel->addcond('M48.notification_tag_id in ('.$searchList['tag_ids'].')');
      }
    }
    // ----- 入力チェック -----
    function M28E01($dataM28) {
      InputCheck($dataM28['template_id'], 'テンプレートID', '', 'N', 9, 0);
      InputCheck($dataM28['template'], 'テンプレート', 'N', '', 0, 30000);
      InputCheck($dataM28['turn'], '並び順', 'N', 'N', 9, 0);
      InputCheck($dataM28['hide_flag'], '非表示区分', '', 'N', 1, 0);
    }
    // -------------------------------------------------
    // 更新
    function M28U01($dataM28, $creator) {
      return $this->updateAllField($dataM28, $creator);
    }
    // -------------------------------------------------
    // 論理削除
    function M28R01($template_id, $creator) {
      $template_id = intval($template_id);
      $dataM28 = $this->CreateNewDto();
      $dataM28['template_id'] = $template_id;
      return $this->removeRecord($dataM28, $creator);
    }
    // -------------------------------------------------
    // 基本
    function __construct() {
      $this->addFieldDef('template_id', DFNUM, FTREAD|FTID);
      $this->addFieldDef('notification_category_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('template_title', DFSTR, FTREAD|FTUPD);
      $this->addFieldDef('question',DFSTR,FTREAD|FTUPD);
      $this->addFieldDef('template', DFSTR, FTREAD|FTUPD);
      $this->addFieldDef('turn', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('hide_flag', DFNUM, FTREAD|FTUPD);
      _daoBase::__construct('M28_reply_mail_templates','M28');
      //join
      $this->addFieldDef('notification_category_name', DFSTR, 0);

    }
  }
  $dbM28 = new M28_reply_mail_templates();
