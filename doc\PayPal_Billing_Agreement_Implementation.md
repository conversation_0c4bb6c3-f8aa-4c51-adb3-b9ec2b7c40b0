# PayPal Billing Agreement Implementation for HealMate

## Overview

This document describes the implementation of PayPal Billing Agreements for recurring payments in the HealMate system. The implementation adds three main functions to handle the complete billing agreement workflow.

## New Functions Added

### 1. `paypalCreateBillingPlan($planData)`

Creates a PayPal billing plan based on data from the M20_plans table.

**Parameters:**
- `$planData`: Array containing plan details from M20 table with fields:
  - `plan_id`: Plan identifier
  - `plan_title`: Plan name
  - `plan_description`: Plan description (optional)
  - `term_type`: 1 for days, other for months
  - `plan_term`: Duration (number of days/months)
  - `plan_price`: Price in JPY

**Returns:**
```php
[
    'success' => true/false,
    'plan_id' => 'PayPal plan ID',
    'data' => 'Full PayPal response',
    'error' => 'Error message if failed'
]
```

**Usage:**
```php
$planData = $dbM20->M20G01($planId);
$result = paypalCreateBillingPlan($planData);
```

### 2. `paypalCreateBillingAgreement($params)`

Creates a billing agreement from a billing plan.

**Parameters:**
- `$params`: Array containing:
  - `plan_id`: PayPal plan ID (from step 1)
  - `subscriber_name`: Customer name (optional)
  - `subscriber_email`: Customer email (optional)
  - `return_url`: Success redirect URL
  - `cancel_url`: Cancel redirect URL

**Returns:**
```php
[
    'success' => true/false,
    'agreement_token' => 'PayPal agreement token',
    'approval_url' => 'URL for customer approval',
    'data' => 'Full PayPal response',
    'error' => 'Error message if failed'
]
```

**Usage:**
```php
$params = [
    'plan_id' => $paypalPlanId,
    'subscriber_name' => $userName,
    'subscriber_email' => $userEmail,
    'return_url' => 'https://my.healmate.jp/payment/success',
    'cancel_url' => 'https://my.healmate.jp/payment/cancel'
];
$result = paypalCreateBillingAgreement($params);
```

### 3. `paypalExecuteBillingAgreement($params)`

Activates the billing agreement after customer approval.

**Parameters:**
- `$params`: Array containing:
  - `payment_token`: PayPal payment token (from step 2)

**Returns:**
```php
[
    'success' => true/false,
    'billing_agreement_id' => 'Billing agreement ID for future charges',
    'status' => 'Agreement status',
    'payer_id' => 'PayPal payer ID',
    'data' => 'Full PayPal response',
    'error' => 'Error message if failed'
]
```

**Usage:**
```php
$params = ['payment_token' => $paymentToken];
$result = paypalExecuteBillingAgreement($params);
```

### 4. `paypalChargeWithBillingAgreement($params)` (Bonus Function)

Charges using an existing billing agreement for recurring payments.

**Parameters:**
- `$params`: Array containing:
  - `billing_agreement_id`: Active billing agreement ID
  - `amount`: Amount to charge
  - `currency`: Currency code (default: 'JPY')
  - `note`: Payment note (optional)

**Returns:**
```php
[
    'success' => true/false,
    'billing_agreement_id' => 'Billing agreement ID used',
    'amount' => 'Amount charged',
    'currency' => 'Currency used',
    'response' => 'PayPal response',
    'error' => 'Error message if failed'
]
```

## Complete Workflow

### Initial Setup (One-time per plan)
1. **Create Billing Plan**: Use plan data from M20 table to create PayPal billing plan
2. **Create Billing Agreement**: Create subscription with customer details
3. **Customer Approval**: Redirect customer to PayPal for approval
4. **Execute Agreement**: Activate the agreement after approval

### Recurring Payments
- Use `paypalChargeWithBillingAgreement()` with the billing agreement ID
- This can be automated via batch jobs (similar to existing `batPayPalCharge.php`)

## Database Integration

The implementation works with existing database structure:

- **M20_plans**: Source of plan details for billing plan creation
- **P07_paypal_tokens**: Can store billing agreement IDs
- **P01_payments**: Records payment transactions
- **U01_users**: Links users to their billing agreements

## Example Integration

See `common/inc/logic/paypal_billing_example.php` for complete usage examples.

## Error Handling

All functions include comprehensive error handling:
- Parameter validation
- HTTP status code checking
- Exception catching
- Detailed error logging

## Security Considerations

- Uses existing PayPal credentials from global configuration
- Includes request ID generation for idempotency
- Follows PayPal's security best practices
- Maintains compatibility with existing authentication

## Testing

Test in sandbox environment using:
- Sandbox credentials in `_global_awsStg.inc`
- PayPal Developer Dashboard for monitoring
- Test customer accounts for approval flow

## Migration from Existing System

The new functions complement existing PayPal integration:
- Existing one-time payment functions remain unchanged
- New billing agreement functions add recurring capability
- Backward compatible with current payment flows
