<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : G03_chats
	class G03_chats extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function G03G01($group_chat_id, $chat_id) {
			$group_chat_id = intval($group_chat_id);
			$chat_id = intval($chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('chat_id='.$chat_id);
			return $this->loadRecord($sel);
		}
		// ----- 最終データ -----
		function G03G02($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('chat_image_id=0');
			$sel->addcond('is_delete=0');
			$sel->addcond('Remove=0');
			$sel->addorder('chat_id DESC');
			return $this->loadRecord($sel);
		}
		// ----- 最終データ既読 -----
		function G03G03($group_chat_id, $user_id) {
			$group_chat_id = intval($group_chat_id);
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('G05.user_id as G05_user_id');
			$sel->addcond('G03.group_chat_id='.$group_chat_id);
			$sel->addcond('G03.is_delete=0');
			$sel->addcond('G03.Remove=0');
			$sel->addorder('G03.chat_id DESC');
			$sel->addleftjoin('G05_chat_reads G05', 'G05.group_chat_id=G03.group_chat_id and G05.chat_id=G03.chat_id and G05.user_id=' . $user_id . ' and G05.Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- Get message with full information -----
		function G03G04($group_chat_id, $chat_id) {
			$group_chat_id = intval($group_chat_id);
			$chat_id = intval($chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.Udate as U02_Udate');
			$sel->addfield('G02.is_certificated');
			$sel->addfield('G02.is_manager');
			$sel->addcond('G03.Remove=0');
			$sel->addleftjoin('G02_group_chat_users G02','G02.group_chat_id=G03.group_chat_id and G02.user_id=G03.user_id and G02.Remove=0');
			$sel->addleftjoin('U01_users U01','U01.user_id=G03.user_id and U01.Remove=0');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=G03.user_id and U02.image_id = 1 and U02.Remove=0');
			$sel->addcond('G03.group_chat_id='.$group_chat_id);
			$sel->addcond('G03.chat_id='.$chat_id);
			return $this->loadRecord($sel);
		}
		// ----- Get previous message -----
		function G03G05($group_chat_id, $chat_id) {
			$group_chat_id = intval($group_chat_id);
			$chat_id = intval($chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('chat_id<'.$chat_id);
			$sel->addcond('Remove=0');
			$sel->addorder('chat_id DESC');
			return $this->loadRecord($sel);
		}
		function G03G06($searchList) {
			$sel = $this->CreateSSql(TRUE);
			if ( !empty($searchList['group_chat_id']) ) {
				$sel->addcond('group_chat_id='.$searchList['group_chat_id']);
			}
			if ( !empty($searchList['chat_kind']) ) {
				$sel->addcond('chat_kind='.$searchList['chat_kind']);
			}
			if ( !empty($searchList['chat']) ) {
				$sel->addcond('chat="'.$searchList['chat'].'"');
			}
			if ( !empty($searchList['is_delete']) ) {
				if ( $searchList['is_delete'] == 99 ) $searchList['is_delete'] = 0;
				$sel->addcond('is_delete='.$searchList['is_delete']);
			}
			$sel->addcond('Remove=0');
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function G03S01($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('Remove=0');
			return $this->loadList($sel);
		}
		// ----- トーク -----
		function G03S02($group_chat_id, $searchList=[]) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addfield('U02.Udate as U02_Udate');
			$sel->addfield('G02.is_certificated');
			$sel->addfield('G02.is_manager');
			$sel->addfield('G04.chat_image_code');
			$sel->addfield('G04.chat_image_extension');
			$sel->addfield('U59.nickname');
			$sel->addcond('G03.group_chat_id='.$group_chat_id);
			$sel->addcond('G03.Remove=0');
			$sel->addleftjoin('U01_users U01','U01.user_id=G03.user_id and U01.Remove=0');
			$sel->addleftjoin('G02_group_chat_users G02','G02.group_chat_id=G03.group_chat_id and G02.user_id=G03.user_id and G02.Remove=0');
			$sel->addleftjoin('G04_chat_images G04', 'G04.group_chat_id=G03.group_chat_id and G04.chat_image_id = G03.chat_image_id and G04.Remove=0');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=G03.user_id and U02.image_id = 1 and U02.Remove=0');
			$sel->addleftjoin('U59_nicknames U59', 'U59.user_id='.$searchList['user_id'].' and U59.nickname_user_id = G03.user_id and U59.Remove=0');
			$sel->addorder('G03.chat_id ASC');
			if ( !empty($searchList['search_limit']) ) {
				$sel->setoffset($searchList['search_offset']);
				$sel->setlimit($searchList['search_limit']);
			}
			return $this->loadList($sel);
		}
		// ----- 管理画面用 -----
		function G03S03($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.dummy_flag');
			$sel->addfield('U01.alert_flag');
			$sel->addfield('U01.user_status');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('U01.forced_private');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('G02.is_certificated');
			$sel->addfield('G08.group_chat_violation_id');
			$sel->addfield('G04.chat_image_code');
			$sel->addfield('G04.chat_image_extension');
			$sel->addcond('G03.group_chat_id='.$group_chat_id);
			$sel->addcond('G03.Remove=0');
			$sel->addleftjoin('G02_group_chat_users G02','G02.group_chat_id=G03.group_chat_id and G02.user_id=G03.user_id and G02.Remove=0');
			$sel->addleftjoin('U01_users U01','U01.user_id=G03.user_id and U01.Remove=0');
			$sel->addleftjoin('G08_group_chat_violations G08', 'G08.group_chat_id=G03.group_chat_id and G08.chat_id=G03.chat_id and G08.Remove=0');
			// ----- 画像 -----
			$sel->addleftjoin('G04_chat_images G04', 'G04.group_chat_id=G03.group_chat_id and G04.chat_image_id = G03.chat_image_id and G04.Remove=0');
			$sel->addorder('G03.chat_id ASC');
			return $this->loadList($sel);
		}
		// ----- 削除対象 -----
		function G03S04($idate) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('chat_image_id>0');
			$sel->addcond('Idate<"'.$idate.'"');
			return $this->loadList($sel);
		}
		// ----- 管理画面用 -----
		function G03S05($searchList) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('G01.group_chat_title');
			$sel->addfield('G01.group_chat_code');
			$sel->addfield('G04.chat_image_code');
			$sel->addfield('G04.chat_image_extension');
			if ( !empty($searchList['user_id']) ) {
				$sel->addcond('G03.user_id='.$searchList['user_id']);
			}
			if ( !empty($searchList['group_chat_id']) ) {
				$sel->addcond('G03.group_chat_id in ('.$searchList['group_chat_id'].')');
			}
			if ( !empty($searchList['chat_kind']) ) {
				$sel->addcond('G03.chat_kind='.$searchList['chat_kind']);
			}
			$sel->addleftjoin('G01_group_chats G01','G01.group_chat_id=G03.group_chat_id');
			$sel->addleftjoin('G04_chat_images G04','G04.group_chat_id=G03.group_chat_id and G04.chat_image_id=G03.chat_image_id');
			$sel->addleftjoin('U01_users U01','U01.user_id=G03.user_id and U01.Remove=0');
			$sel->addorder('G03.Udate DESC');
			return $this->loadList($sel);
		}
		// ----- 管理画面用 -----
		function G03S06($searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_sex');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.dummy_flag');
			$sel->addfield('U01.alert_flag');
			$sel->addfield('U01.user_status');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('U01.forced_private');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('G02.is_certificated');
			$sel->addfield('G08.group_chat_violation_id');
			$sel->addfield('G04.chat_image_code');
			$sel->addfield('G04.chat_image_extension');
			if ( !empty($searchList['group_chat_id']) ) {
				$sel->addcond('G03.group_chat_id='.$searchList['group_chat_id']);
			}
			if ( !empty($searchList['date_start']) ) {
				$sel->addcond('G03.Idate>="'.$searchList['date_start'].'"');
			}
			if ( !empty($searchList['date_finish']) ) {
				$sel->addcond('G03.Idate<="'.$searchList['date_finish'].'"');
			}
			$sel->addcond('G03.Remove=0');
			$sel->addleftjoin('G02_group_chat_users G02','G02.group_chat_id=G03.group_chat_id and G02.user_id=G03.user_id and G02.Remove=0');
			$sel->addleftjoin('U01_users U01','U01.user_id=G03.user_id and U01.Remove=0');
			$sel->addleftjoin('G08_group_chat_violations G08', 'G08.group_chat_id=G03.group_chat_id and G08.chat_id=G03.chat_id and G08.Remove=0');
			// ----- 画像 -----
			$sel->addleftjoin('G04_chat_images G04', 'G04.group_chat_id=G03.group_chat_id and G04.chat_image_id = G03.chat_image_id and G04.Remove=0');
			$sel->addorder('G03.chat_id ASC');
			return $this->loadList($sel);
		}
		// ----- 件数 -----
		function G03C01($group_chat_id, $chat_id, $user_id) {
			$group_chat_id = intval($group_chat_id);
			$chat_id = intval($chat_id);
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('chat_id>'.$chat_id);
			$sel->addcond('user_id!='.$user_id);
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(chat_id)');
		}
		function G03C02($group_chat_id) {
			$group_chat_id = intval($group_chat_id);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('group_chat_id='.$group_chat_id);
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(chat_id)');
		}
		// ----- 入力チェック -----
		function G03E01($dataG03) {
			InputCheck($dataG03['group_chat_id'], 'グループチャットID', 'N', 'N', 9, 0);
			InputCheck($dataG03['chat_id'], 'チャットID', '', 'N', 9, 0);
			InputCheck($dataG03['user_id'], '会員ID', '', 'N', 9, 0);
			InputCheck($dataG03['administrator_id'], '管理者ID', '', 'N', 9, 0);
			InputCheck($dataG03['chat_kind'], 'チャット種別', '', 'N', 4, 0);
			InputCheck($dataG03['chat_image_id'], '画像ID', '', 'N', 9, 0);
			InputCheck($dataG03['chat'], 'チャット', '', '', 0, 30000);
			InputCheck($dataG03['read_count'], '既読人数', '', 'N', 1, 0);
			InputCheck($dataG03['is_delete'], '削除', '', 'N', 1, 0);
		}
		// -------------------------------------------------
		// 更新
		function G03U01($dataG03, $creator) {
			return $this->updateAllField($dataG03, $creator);
		}
		// 既読のカウント
		function G03U02($groupChatId, $chatId, $remoteAddr) {
			$mainDB = MainDB();
			$sql = new query();
			$sql->addsql("
				UPDATE G03_chats 
				SET read_count = read_count + 1,
					Udate = CURRENT_TIMESTAMP,
					Uuser = '".$remoteAddr."'
				WHERE group_chat_id = ".$groupChatId."
				AND chat_id = ".$chatId."
				AND Remove = 0
			");			
			return $sql->exec($mainDB);
		}		
		// -------------------------------------------------
		// 論理削除
		function G03R01($group_chat_id, $chat_id, $creator) {
			$group_chat_id = intval($group_chat_id);
			$chat_id = intval($chat_id);
			$dataG03 = $this->CreateNewDto();
			$dataG03['group_chat_id'] = $group_chat_id;
			$dataG03['chat_id'] = $chat_id;
			return $this->removeRecord($dataG03, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('group_chat_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('chat_id', DFNUM, FTREAD|FTID);
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('administrator_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('chat_kind', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('chat_image_id', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('chat', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('read_count', DFNUM, FTREAD|FTUPD);
			$this->addFieldDef('is_delete', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('G03_chats','G03');
			// join U01
			$this->addFieldDef('user_code', DFSTR, 0);
			$this->addFieldDef('user_nickname', DFSTR, 0);
			$this->addFieldDef('user_sex', DFNUM, 0);
			$this->addFieldDef('user_birthday', DFNUM, 0);
			$this->addFieldDef('dummy_flag', DFNUM, 0);
			$this->addFieldDef('alert_flag', DFNUM, 0);
			$this->addFieldDef('user_status', DFNUM, 0);
			$this->addFieldDef('none_public_status', DFNUM, 0);
			$this->addFieldDef('forced_private', DFNUM, 0);
			// join U02
			$this->addFieldDef('image_id', DFNUM, 0);
			$this->addFieldDef('image_code', DFSTR, 0);
			$this->addFieldDef('image_extension', DFSTR, 0);
			$this->addFieldDef('is_unpublish', DFNUM, 0);
			$this->addFieldDef('version_id', DFSTR, 0);
			$this->addFieldDef('publish_version_id', DFSTR, 0);
			$this->addFieldDef('thumbnail_version_id', DFSTR, 0);
			$this->addFieldDef('U02_Udate', DFSTR, 0);
			// join U59
			$this->addFieldDef('nickname', DFSTR, 0);
			// join G01
			$this->addFieldDef('group_chat_title', DFSTR, 0);
			$this->addFieldDef('group_chat_code', DFSTR, 0);
			// join G02
			$this->addFieldDef('is_certificated', DFNUM, 0);
			$this->addFieldDef('is_manager', DFNUM, 0);
			// join G04
			$this->addFieldDef('chat_image_code', DFSTR, 0);
			$this->addFieldDef('chat_image_extension', DFSTR, 0);
			// join G05
			$this->addFieldDef('G05_user_id', DFNUM, 0);
			// join G08
			$this->addFieldDef('group_chat_violation_id', DFNUM, 0);
		}
		
	}
	$dbG03 = new G03_chats();
