<?php
  include_once('_daobase.inc');
  // -----------------------------------------------------
  // Table : M47_notification_tags
  class M47_notification_tags extends _daoBase {
    // -------------------------------------------------
    // 検索
    // ----- PKで1件 -----
    function M47G01($notification_tag_id) {
      $notification_tag_id = intval($notification_tag_id);
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('notification_tag_id='.$notification_tag_id);
      return $this->loadRecord($sel);
    }
    function M47G02($notification_tag_name) {
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('notification_tag_name="'.$notification_tag_name.'"');
			$sel->addorder('priority_flag desc, turn');
      return $this->loadRecord($sel);
    }
    // ----- リスト -----
    function M47S01() {
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('Remove=0');
			$sel->addorder('priority_flag desc, turn');
      return $this->loadList($sel);
    }
    // ----- 検索 -----
    function M47S02($searchList=[]) {
      $sel = $this->CreateSSql(TRUE);
			$this->addcondition($sel, $searchList);
			$sel->addorder('priority_flag desc, turn');
      return $this->loadList($sel);
    }
		// ----- smarty用表示リスト -----
		function M47S03() {
			$sel = $this->CreateSSql(TRUE);
      $sel->addcond('Remove=0');
			$sel->addorder('priority_flag desc, turn');
			$listM47 = $this->loadList($sel);
			$datalist = array();
			foreach ( $listM47 as $dataM47 ) {
				$datalist[$dataM47['notification_tag_id']] = $dataM47['notification_tag_name'];
			}
			return $datalist;
		}
    // ----- 検索 -----
    function M47S04($searchList=[]) {
      $sel = $this->CreateSSql(FALSE);
			$this->addcondition($sel, $searchList);
			$sel->addfield('count(M48.template_id) as count_template_id');
			$sel->addfield('max(notification_tag_name) as notification_tag_name');
			$sel->addfield('max(priority_flag) as priority_flag');
      $sel->addfield('M47.notification_tag_id');
			$sel->addinner('M48_reply_mail_template_tags M48','M48.notification_tag_id=M47.notification_tag_id');
			$sel->addgroup('M47.notification_tag_id');
			$sel->addorder('M47.priority_flag desc, M47.turn');
      return $this->loadList($sel);
    }    
    function M47C01($searchList=[]) {
      $sel = $this->CreateSSql(FALSE);
			$this->addcondition($sel, $searchList);
      return $this->loadCalcResult($sel, 'COUNT(notification_tag_id)');
    }
		// ----- 抽出条件 -----
		private function addcondition(&$sel, $searchList) {
      if ( !empty($searchList['notification_tag_id']) ) $sel->addcond('M47.notification_tag_id='.$searchList['notification_tag_id']);
      if (!empty($searchList['notification_tag_name'])) {
        $sel->addcond('M47.notification_tag_name like "%'.$searchList['notification_tag_name'].'%"');
      }
      if ( !empty($searchList['turn']) ) $sel->addcond('M47.turn='.$searchList['turn']);
      if ( !empty($searchList['priority_flag']) ) {
				if ( $searchList['priority_flag'] == 99 ) $searchList['priority_flag'] = 0;
        $sel->addcond('M47.priority_flag='.$searchList['priority_flag']);
      }
      $sel->addcond('M47.Remove=0');
		}
    // ----- 入力チェック -----
    function M47E01($dataM47) {
      InputCheck($dataM47['notification_tag_id'], 'ID', '', 'N', 9, 0);
      InputCheck($dataM47['notification_tag_name'], 'タグ名', 'N', '', 0, 120);
    }
    // -------------------------------------------------
    // 更新
    function M47U01($dataM47, $creator) {
      return $this->updateAllField($dataM47, $creator);
    }
    // -------------------------------------------------
    // 論理削除
    function M47R01($notification_tag_id,  $creator) {
      $notification_tag_id = intval($notification_tag_id);
      $dataM47 = $this->CreateNewDto();
      $dataM47['notification_tag_id'] = $notification_tag_id;
      return $this->removeRecord($dataM47, $creator);
    }
    // 論理削除
    function M47R02($notification_tag_id) {
      $notification_tag_id = intval($notification_tag_id);
      $dataM47 = $this->CreateNewDto();
      $dataM47['notification_tag_id'] = $notification_tag_id;
      $this->removeRecord($dataM47, $creator);
      //関連するテーブルも論理削除
			$sql = new query;
			$sql->addsql('update M48_reply_mail_template_tags set remove=1 where notification_tag_id='.$notification_tag_id);
			$mainDB = MainDB();
			$sql->exec($mainDB);
			$sql = new query;
			$sql->addsql('update M46_notification_case_tags set remove=1 where notification_tag_id='.$notification_tag_id);
			$mainDB = MainDB();
			$sql->exec($mainDB);

    }    
    // -------------------------------------------------
    // 基本
    function __construct() {
      $this->addFieldDef('notification_tag_id',DFNUM,FTREAD|FTID);
      $this->addFieldDef('notification_tag_name',DFSTR,FTREAD|FTUPD);
      $this->addFieldDef('turn',DFNUM,FTREAD|FTUPD);
      $this->addFieldDef('priority_flag',DFNUM,FTREAD|FTUPD);
      _daoBase::__construct('M47_notification_tags','M47');
      $this->addFieldDef('count_template_id',DFNUM,0);
    }
  }
  $dbM47 = new M47_notification_tags();
