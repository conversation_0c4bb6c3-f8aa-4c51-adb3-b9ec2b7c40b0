<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : U07_footprints
	class U07_footprints extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function U07G01($user_id, $footprint_user_id) {
			$user_id = intval($user_id);
			$footprint_user_id = intval($footprint_user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_id='.$user_id);
			$sel->addcond('footprint_user_id='.$footprint_user_id);
			return $this->loadRecord($sel);
		}
		// ----- リスト -----
		function U07S01($dataU01) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_id');
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.redidence_prefecture_code');
			$sel->addfield('U01.one_word');
			$sel->addfield('U01.identify_confirm');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addfield('U02.Udate as U02_Udate');
			$sel->addfield('U59.nickname');
			$sel->addfield('M20.plan_type');
			$sel->addcond('U07.user_id='.$dataU01['user_id']);
			$sel->addcond('U07.Remove=0');
			$sel->addinner('U01_users U01','U01.user_id=U07.footprint_user_id');
			$sel->addcond('(U01.none_public_status=0 || U01.none_public_status=3 || U01.none_public_status=4)');
			if ( empty($dataU01['forced_private']) ) {
				// ----- 自分が正常会員なら正常会員分のみ -----
				$sel->addcond('U01.forced_private=0');
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addorder('U07.Udate DESC');
			$sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			$sel->addleftjoin('U59_nicknames U59', 'U59.user_id=U07.user_id and U59.nickname_user_id=U07.footprint_user_id and U59.Remove=0');
			// ----- ごめんなさい除外 -----
			$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0803','U0803.like_user_id=U07.footprint_user_id');
			$sel->addcond('U0803.like_user_id IS NULL');
			$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0804','U0804.user_id=U07.footprint_user_id');
			$sel->addcond('U0804.user_id IS NULL');
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $dataU01['user_id'] . ') U24','U24.block_user_id=U07.footprint_user_id');
			$sel->addcond('U24.block_user_id IS NULL');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U07.footprint_user_id and U02.image_id = 1 and U02.Remove=0');
			return $this->loadList($sel);
		}
		// ----- 足あとしたリスト -----
		function U07S02($footprint_user_id) {
			$footprint_user_id = intval($footprint_user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.redidence_prefecture_code');
			$sel->addfield('U01.one_word');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('U01.user_status');
			$sel->addfield('U01.withdrawal_date');
			$sel->addfield('U01.dummy_flag');
			$sel->addfield('U01.alert_flag');
			$sel->addfield('U01.forced_private');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addcond('U07.footprint_user_id='.$footprint_user_id);
			$sel->addcond('U07.Remove=0');
			$sel->addinner('U01_users U01','U01.user_id=U07.user_id');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U07.user_id and U02.image_id = 1 and U02.Remove=0 and U02.is_unpublish=0'); //公開中
			$sel->addorder('U07.Udate DESC');
			return $this->loadList($sel);
		}		
		// ----- 足あとされたリスト -----
		function U07S03($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.redidence_prefecture_code');
			$sel->addfield('U01.one_word');
			$sel->addfield('U01.identify_confirm');
			$sel->addfield('U01.none_public_status');
			$sel->addfield('U01.user_status');
			$sel->addfield('U01.withdrawal_date');
			$sel->addfield('U01.dummy_flag');
			$sel->addfield('U01.alert_flag');
			$sel->addfield('U01.forced_private');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addfield('M20.plan_type');
			$sel->addcond('U07.user_id='.$user_id);
			$sel->addcond('U07.Remove=0');
			$sel->addinner('U01_users U01','U01.user_id=U07.footprint_user_id');
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U01.user_id and U02.image_id = 1 and U02.Remove=0 and U02.is_unpublish=0'); //公開中
			$sel->addorder('U07.Udate DESC');
			return $this->loadList($sel);
		}
		// ----- 足あと用 footprint_user_idsで検索 -----
		function U07S04($dataU01, $footprintUserIds) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('U01.user_id');
			$sel->addfield('U01.user_code');
			$sel->addfield('U01.user_nickname');
			$sel->addfield('U01.user_birthday');
			$sel->addfield('U01.redidence_prefecture_code');
			$sel->addfield('U01.one_word');
			$sel->addfield('U01.identify_confirm');
			$sel->addfield('U02.image_id');
			$sel->addfield('U02.image_code');
			$sel->addfield('U02.image_extension');
			$sel->addfield('U02.is_unpublish');
			$sel->addfield('U02.version_id');
			$sel->addfield('U02.publish_version_id');
			$sel->addfield('U02.thumbnail_version_id');
			$sel->addfield('U02.Udate as U02_Udate');
			$sel->addfield('U59.nickname');
			$sel->addfield('M20.plan_type');
			$sel->addcond('U07.user_id='.$dataU01['user_id']);
			$sel->addcond('U07.Remove=0');
			$sel->addinner('U01_users U01','U01.user_id=U07.footprint_user_id');
			$sel->addcond('(U01.none_public_status=0 || U01.none_public_status=3 || U01.none_public_status=4)');
			if ( empty($dataU01['forced_private']) ) {
				// ----- 自分が正常会員なら正常会員分のみ -----
				$sel->addcond('U01.forced_private=0');
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			$sel->addleftjoin('M20_plans M20', 'M20.plan_id=U01.plan_id and M20.Remove=0');
			$sel->addleftjoin('U59_nicknames U59', 'U59.user_id=U07.user_id and U59.nickname_user_id=U07.footprint_user_id and U59.Remove=0');
			// ----- ごめんなさい除外 -----
			$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0803','U0803.like_user_id=U07.footprint_user_id');
			$sel->addcond('U0803.like_user_id IS NULL');
			$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0804','U0804.user_id=U07.footprint_user_id');
			$sel->addcond('U0804.user_id IS NULL');
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $dataU01['user_id'] . ') U24','U24.block_user_id=U07.footprint_user_id');
			$sel->addcond('U24.block_user_id IS NULL');
			// ----- メイン画像 -----
			$sel->addleftjoin('U02_user_images U02', 'U02.user_id=U07.footprint_user_id and U02.image_id = 1 and U02.Remove=0');
			
			// ----- footprint_user_idによる検索 -----
			if (empty($footprintUserIds)) {
				return [];
			} else {
				$userIds = implode(',', $footprintUserIds);
				$sel->addcond('U07.footprint_user_id in ('.$userIds.')');
				$sel->addorder("FIELD(U07.footprint_user_id, $userIds)");
			}
			
			return $this->loadList($sel);
		}
		// ----- 未確認足あと数 -----
		function U07C01($dataU01, $udate) {
			$udate = date('Y-m-d H:i:s', strtotime($udate));
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('U07.user_id='.$dataU01['user_id']);
			$sel->addcond('U07.Udate>"'.$udate.'"');
			$sel->addcond('U07.Remove=0');
			$sel->addinner('U01_users U01','U01.user_id=U07.footprint_user_id');
			$sel->addcond('(U01.none_public_status=0 or U01.none_public_status=3 or U01.none_public_status=4)');
			if ( empty($dataU01['forced_private']) ) {
				// ----- 自分が正常会員なら正常会員分のみ -----
				$sel->addcond('U01.forced_private=0');
			}
			$sel->addcond('U01.user_status=0');
			$sel->addcond('U01.Remove=0');
			// ----- ごめんなさい除外 -----
			$sel->addleftjoin('(select like_user_id from U08_likes where user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0803','U0803.like_user_id=U07.footprint_user_id');
			$sel->addcond('U0803.like_user_id IS NULL');
			$sel->addleftjoin('(select user_id from U08_likes where like_user_id = ' . $dataU01['user_id'] . ' and is_no_like = 1) U0804','U0804.user_id=U07.footprint_user_id');
			$sel->addcond('U0804.user_id IS NULL');
			// ----- ブロックリスト除外 -----
			$sel->addleftjoin('(select block_user_id from U24_block_targets where user_id = ' . $dataU01['user_id'] . ') U24','U24.block_user_id=U07.footprint_user_id');
			$sel->addcond('U24.block_user_id IS NULL');
			return $this->loadCalcResult($sel, 'COUNT(U07.user_id)');
		}
		// ----- 入力チェック -----
		function U07E01($dataU07) {
			InputCheck($dataU07['user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataU07['footprint_user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataU07['foot_count'], '訪問回数', 'N', 'N', 9, 0);
		}
		// -------------------------------------------------
		// 更新
		function U07U01($dataU07, $creator) {
			return $this->updateAllField($dataU07, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function U07R01($user_id, $footprint_user_id, $creator) {
			$user_id = intval($user_id);
			$footprint_user_id = intval($footprint_user_id);
			$dataU07 = $this->CreateNewDto();
			$dataU07['user_id'] = $user_id;
			$dataU07['footprint_user_id'] = $footprint_user_id;
			return $this->removeRecord($dataU07, $creator);
		}
		// ----- 全削除 -----
		function U07R02($user_id, $creator) {
			$user_id = intval($user_id);
			$upd = new usql();
			$upd->settable('U07_footprints');
			$upd->addfield(DFNUM,"Remove",1);
			$upd->addcond('user_id='.$user_id);
			$upd->addcond('Remove=0');
			return $this->execSql($upd, FALSE, $creator);
		}
		function U07R03($footprint_user_id, $creator) {
			$footprint_user_id = intval($footprint_user_id);
			$upd = new usql();
			$upd->settable('U07_footprints');
			$upd->addfield(DFNUM,"Remove",1);
			$upd->addcond('footprint_user_id='.$footprint_user_id);
			$upd->addcond('Remove=0');
			return $this->execSql($upd, FALSE, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('footprint_user_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('foot_count', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('U07_footprints','U07');
			// join U01
			$this->addFieldDef('user_id', DFNUM, 0);
			$this->addFieldDef('user_code', DFSTR, 0);
			$this->addFieldDef('user_nickname', DFSTR, 0);
			$this->addFieldDef('user_birthday', DFSTR, 0);
			$this->addFieldDef('redidence_prefecture_code', DFSTR, 0);
			$this->addFieldDef('one_word', DFSTR, 0);
			$this->addFieldDef('identify_confirm', DFNUM, 0);
			$this->addFieldDef('none_public_status', DFNUM, 0);
			$this->addFieldDef('user_status', DFNUM, 0);
			$this->addFieldDef('withdrawal_date', DFNUM, 0);
			$this->addFieldDef('dummy_flag', DFNUM, 0);
			$this->addFieldDef('alert_flag', DFNUM, 0);
			$this->addFieldDef('forced_private', DFNUM, 0);
			// join U02
			$this->addFieldDef('image_id', DFNUM, 0);
			$this->addFieldDef('image_code', DFSTR, 0);
			$this->addFieldDef('image_extension', DFSTR, 0);
			$this->addFieldDef('is_unpublish', DFNUM, 0);
			$this->addFieldDef('version_id', DFSTR, 0);
			$this->addFieldDef('publish_version_id', DFSTR, 0);
			$this->addFieldDef('thumbnail_version_id', DFSTR, 0);
			$this->addFieldDef('U02_Udate', DFSTR, 0);
			// join U59
			$this->addFieldDef('nickname', DFSTR, 0);
			// join M20
			$this->addFieldDef('plan_type', DFNUM, 0);
		}
	}
	$dbU07 = new U07_footprints();
