<?php
  include_once('_daobase.inc');
  // -----------------------------------------------------
  // Table : G08_group_chat_violations
  class G08_group_chat_violations extends _daoBase {
    // -------------------------------------------------
    // 検索
    // ----- PKで1件 -----
    function G08G01($group_chat_violation_id) {
      $group_chat_violation_id = intval($group_chat_violation_id);
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('group_chat_violation_id='.$group_chat_violation_id);
      return $this->loadRecord($sel);
    }
		function G08G02($group_chat_violation_id) {
			$group_chat_violation_id = intval($group_chat_violation_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('M33.group_chat_comment_violation');
			$sel->addfield('G01.group_chat_title');
			$sel->addfield('G03.chat');
			$sel->addfield('DATE(G03.Idate - INTERVAL 3 DAY) as G03_Idate');
			$sel->addcond('G08.group_chat_violation_id='.$group_chat_violation_id);
			$sel->addcond('G08.Remove=0');
			$sel->addinner('G01_group_chats G01', 'G01.group_chat_id=G08.group_chat_id');
			$sel->addleftjoin('G03_chats G03', 'G03.group_chat_id=G08.group_chat_id and G03.chat_id=G08.chat_id and G03.Remove=0');
			$sel->addinner('M33_group_chat_comment_violations M33', 'M33.group_chat_comment_violation_id=G08.group_chat_comment_violation_id');
			$sel->addcond('M33.Remove=0');
			return $this->loadRecord($sel);
		}
    // ----- リスト -----
    function G08S01($searchList) {
      $sel = $this->CreateSSql(TRUE);
      $sel->addcond('Remove=0');
      if ( !empty($searchlist['limit']) ) {
        $sel->setoffset(($searchList['page'] - 1) * $searchlist['limit']);
        $sel->setlimit($searchlist['limit']);
      }
      return $this->loadList($sel);
    }
		function G08S02($searchList) {
			$sel = $this->CreateSSql(TRUE);
			$sel->addfield('report.user_code as report_code');
			$sel->addfield('report.user_nickname as report_nickname');
			$sel->addfield('report.user_sex as report_sex');
			$sel->addfield('report.user_mailaddress as report_mailaddress');
			$sel->addfield('violation.user_code as violation_code');
			$sel->addfield('violation.user_nickname as violation_nickname');
			$sel->addfield('violation.user_sex as violation_sex');
			$sel->addfield('violation.user_mailaddress as violation_mailaddress');
			$sel->addfield('G01.group_chat_title');
			$sel->addfield('G01.group_chat_text');
			$sel->addfield('G03.chat');
			$sel->addfield('M33.group_chat_comment_violation');
			if ( $searchList['is_support'] != 99 ) {
				$sel->addcond('G08.is_support='.$searchList['is_support']);
			}
			if ( !empty($searchList['date_start']) ) {
				$sel->addcond('G08.Idate>="'.$searchList['date_start'].'"');
			}
			if ( !empty($searchList['date_finish']) ) {
				$arDate = explode('-', $searchList['date_finish']);
				$searchList['date_finish'] = date('Y-m-d', mktime(0, 0, 0, $arDate[1], $arDate[2] + 1, $arDate[0]));
				$sel->addcond('G08.Idate<"'.$searchList['date_finish'].'"');
			}
			$sel->addcond('G08.Remove=0');
			$sel->addinner('U01_users report', 'report.user_id=G08.user_id');
			$sel->addcond('report.Remove=0');
			$sel->addinner('U01_users violation', 'violation.user_id=G08.violation_user_id');
			$sel->addcond('violation.Remove=0');
			$sel->addinner('G01_group_chats G01', 'G01.group_chat_id=G08.group_chat_id');
			$sel->addleftjoin('G03_chats G03', 'G03.group_chat_id=G08.group_chat_id and G03.chat_id=G08.chat_id and G03.Remove=0');
			$sel->addinner('M33_group_chat_comment_violations M33', 'M33.group_chat_comment_violation_id=G08.group_chat_comment_violation_id');
			$sel->addcond('M33.Remove=0');
			$sel->addorder('G08.group_chat_violation_id DESC');
			return $this->loadList($sel);
		}
    // ----- 件数 -----
    function G08C01() {
      $sel = $this->CreateSSql(FALSE);
      $sel->addcond('Remove=0');
      return $this->loadCalcResult($sel, 'COUNT(group_chat_violation_id)');
    }
		// ----- 件数 -----
		function G08C02($is_support) {
			$is_support = intval($is_support);
			$sel = $this->CreateSSql(FALSE);
			$sel->addcond('is_support='.$is_support);
			$sel->addcond('Remove=0');
			return $this->loadCalcResult($sel, 'COUNT(group_chat_violation_id)');
		}
    // ----- 入力チェック -----
    function G08E01($dataG08) {
      InputCheck($dataG08['group_chat_violation_id'], 'グループチャット違反通報ID', '', 'N', 9, 0);
      InputCheck($dataG08['user_id'], '会員ID', '', 'N', 9, 0);
      InputCheck($dataG08['group_chat_id'], 'グループチャットID', 'N', 'N', 9, 0);
      InputCheck($dataG08['chat_id'], 'チャットID', '', 'N', 9, 0);
      InputCheck($dataG08['violation_user_id'], '違反者会員ID', 'N', 'N', 9, 0);
      InputCheck($dataG08['group_chat_comment_violation_id'], 'グループチャットコメント違反ID', 'N', 'N', 9, 0);
      InputCheck($dataG08['group_chat_violation_text'], 'グループチャットコメント違反内容', '', '', 0, 30000);
      InputCheck($dataG08['is_support'], '対応区分', '', 'N', 1, 0);
      InputCheck($dataG08['support_contents'], '応対内容', '', '', 0, 30000);
    }
    // -------------------------------------------------
    // 更新
    function G08U01($dataG08, $creator) {
      return $this->updateAutoIncrementAllField($dataG08, $creator);
    }
    // -------------------------------------------------
    // 論理削除
    function G08R01($group_chat_violation_id, $creator) {
      $group_chat_violation_id = intval($group_chat_violation_id);
      $dataG08 = $this->CreateNewDto();
      $dataG08['group_chat_violation_id'] = $group_chat_violation_id;
      return $this->removeRecord($dataG08, $creator);
    }
    // -------------------------------------------------
    // 基本
    function __construct() {
      $this->addFieldDef('group_chat_violation_id', DFNUM, FTREAD|FTID);
      $this->addFieldDef('user_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('group_chat_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('chat_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('violation_user_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('group_chat_comment_violation_id', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('group_chat_violation_text', DFSTR, FTREAD|FTUPD);
      $this->addFieldDef('is_support', DFNUM, FTREAD|FTUPD);
      $this->addFieldDef('support_contents', DFSTR, FTREAD|FTUPD);
      _daoBase::__construct('G08_group_chat_violations','G08');
			// join U01
			$this->addFieldDef('report_nickname', DFSTR, 0);
			$this->addFieldDef('report_code', DFSTR, 0);
			$this->addFieldDef('report_sex', DFNUM, 0);
			$this->addFieldDef('report_mailaddress', DFSTR, 0);
			$this->addFieldDef('violation_nickname', DFSTR, 0);
			$this->addFieldDef('violation_code', DFSTR, 0);
			$this->addFieldDef('violation_sex', DFNUM, 0);
			$this->addFieldDef('violation_mailaddress', DFSTR, 0);
			// join G01
			$this->addFieldDef('group_chat_title', DFSTR, 0);
			$this->addFieldDef('group_chat_text', DFSTR, 0);
			// join G03
			$this->addFieldDef('chat', DFSTR, 0);
			$this->addFieldDef('G03_Idate', DFSTR, 0);
			// join M33
			$this->addFieldDef('group_chat_comment_violation', DFSTR, 0);
    }
  }
  $dbG08 = new G08_group_chat_violations();
