<?php
	include_once('_daobase.inc');
	// -----------------------------------------------------
	// Table : P07_paypal_tokens
	class P07_paypal_tokens extends _daoBase {
		// -------------------------------------------------
		// 検索
		// ----- PKで1件 -----
		function P07G01($user_id, $paypal_token_id) {
			$user_id = intval($user_id);
			$paypal_token_id = intval($paypal_token_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_id='.$user_id);
			$sel->addcond('paypal_token_id='.$paypal_token_id);
			return $this->loadRecord($sel);
		}
		// ----- 最新1件 -----
		function P07G02($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_id='.$user_id);
			$sel->addcond('Remove=0');
			$sel->addorder('paypal_token_id DESC');
			return $this->loadRecord($sel);
		}
		// ----- トークンのみ1件 -----
		function P07G03($user_id) {
			$user_id = intval($user_id);
			$sel = $this->CreateSSql(TRUE);
			$sel->addcond('user_id='.$user_id);
			$sel->addcond('Remove=0');
			$sel->addcond('order_id=0');
			$sel->addorder('paypal_token_id DESC');
			return $this->loadRecord($sel);
		}		
		// ----- 入力チェック -----
		function P07E01($dataP07) {
			InputCheck($dataP07['user_id'], '会員ID', 'N', 'N', 9, 0);
			InputCheck($dataP07['paypal_token_id'], 'PayPalトークンID', 'N', 'N', 9, 0);
			InputCheck($dataP07['paypal_token'], 'PayPalトークン', 'N', 'H', 255, 0);
			InputCheck($dataP07['order_id'], '発行オーダーID', '', 'N', 9, 0);
		}
		// -------------------------------------------------
		// 更新
		function P07U01($dataP07, $creator) {
			return $this->updateAllField($dataP07, $creator);
		}
		// -------------------------------------------------
		// 論理削除
		function P07R01($user_id, $paypal_token_id, $creator) {
			$user_id = intval($user_id);
			$paypal_token_id = intval($paypal_token_id);
			$dataP07 = $this->CreateNewDto();
			$dataP07['user_id'] = $user_id;
			$dataP07['paypal_token_id'] = $paypal_token_id;
			return $this->removeRecord($dataP07, $creator);
		}
		// -------------------------------------------------
		// 基本
		function __construct() {
			$this->addFieldDef('user_id', DFNUM, FTREAD|FTKEY);
			$this->addFieldDef('paypal_token_id', DFNUM, FTREAD|FTID);
			$this->addFieldDef('paypal_token', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('paypal_order_id', DFSTR, FTREAD|FTUPD);
			$this->addFieldDef('order_id', DFNUM, FTREAD|FTUPD);
			_daoBase::__construct('P07_paypal_tokens','P07');
		}
	}
	$dbP07 = new P07_paypal_tokens();
