<?php
include_once $gLIB_PATH . 'vendor/autoload.php';

use PaypalServerSdkLib\PaypalServerSdkClient;
use PaypalServerSdkLib\PaypalServerSdkClientBuilder;
use PaypalServerSdkLib\Authentication\ClientCredentialsAuthCredentialsBuilder;
use PaypalServerSdkLib\Logging\LoggingConfigurationBuilder;
use PaypalServerSdkLib\Logging\RequestLoggingConfigurationBuilder;
use PaypalServerSdkLib\Logging\ResponseLoggingConfigurationBuilder;
use PaypalServerSdkLib\Models\Builders\OrderRequestBuilder;
use PaypalServerSdkLib\Models\CheckoutPaymentIntent;
use PaypalServerSdkLib\Models\Builders\PurchaseUnitRequestBuilder;
use PaypalServerSdkLib\Models\Builders\AmountWithBreakdownBuilder;
use PaypalServerSdkLib\Environment;
use PaypalServerSdkLib\Controllers\OrdersController;
use Psr\Log\LogLevel;

// Init paypal client
function paypalInit() {
    try {
        global $gPAYPALClientID, $gPAYPALSecret;

        $client = PaypalServerSdkClientBuilder::init()
        ->clientCredentialsAuthCredentials(
            ClientCredentialsAuthCredentialsBuilder::init(
                $gPAYPALClientID,
                $gPAYPALSecret
            )
        )
        ->environment(Environment::SANDBOX)
        //->loggingConfiguration(
        //    LoggingConfigurationBuilder::init()
        //        ->level(LogLevel::INFO)
        //        ->requestConfiguration(RequestLoggingConfigurationBuilder::init()->body(true))
        //        ->responseConfiguration(ResponseLoggingConfigurationBuilder::init()->headers(true))
        //)
        ->build();

        return $client;
    } catch (Exception $e) {
        error_log("PayPal initialization error: " . $e->getMessage());
        throw new Exception("Failed to initialize PayPal client");
    }
}


// Create paypal order
// function paypalCreateOrder($params) {
//     $amount = $params['amount'] ?? null;
//     $currency = $params['currency'] ?? 'JPY';

//     // validate amount and currency
//     if (empty($amount) || empty($currency)) {
//         throw new Exception("Amount and currency are required");
//     }

//     $client = paypalInit();
//     $ordersController = $client->getOrdersController();

//     $collect = [
//         'body' => OrderRequestBuilder::init(
//             CheckoutPaymentIntent::CAPTURE,
//             [
//                 PurchaseUnitRequestBuilder::init(
//                     AmountWithBreakdownBuilder::init(
//                         $currency,
//                         $amount
//                     )->build()
//                 )->build()
//             ]
//         )->build(),
//         'prefer' => 'return=minimal'
//     ];

//     $apiResponse = $ordersController->createOrder($collect);
//     if ($apiResponse->isSuccess()) {
//         $result = $apiResponse->getResult();
//     } else {
//         $result = $apiResponse->getBody();
//     }

//     return $result;
// }


// Authorize paypal order
function paypalAuthorizeOrder($params) {
    // Not yet implemented
    return null;
}

// Capture paypal order
function paypalCaptureOrder($params) {
    $order_id = $params['order_id'];

    // validate order id
    if (empty($order_id)) {
        throw new Exception("Order ID is required");
    }

    $client = paypalInit();
    $ordersController = $client->getOrdersController();

    $collect = [
        'id' => $order_id,
        'prefer' => 'return=minimal'
    ];

    $apiResponse = $ordersController->captureOrder($collect);
    if ($apiResponse->isSuccess()) {
        $result = $apiResponse->getResult();
    } else {
        $result = $apiResponse->getBody();
    }

    return $result;
    
}

// Get paypal order
function paypalGetOrder($params) {
    $order_id = $params['order_id'];

    // validate order id
    if (empty($order_id)) {
        throw new Exception("Order ID is required");
    }

    $client = paypalInit();
    $ordersController = $client->getOrdersController();

    $collect = [
        'id' => $order_id,
        'prefer' => 'return=minimal'
    ];

    $apiResponse = $ordersController->getOrder($collect);
    if ($apiResponse->isSuccess()) {
        $result = $apiResponse->getResult();
    } else {
        $result = $apiResponse->getBody();
    }

    return $result;
}



function getPaypalAccessToken() {
    global $gPAYPALClientID, $gPAYPALSecret, $gPAYPAL_URL;

    $url = $gPAYPAL_URL . "v1/oauth2/token";
    $headers = ["Accept: application/json"];
    $postFields = "grant_type=client_credentials";

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_USERPWD, $gPAYPALClientID . ":" . $gPAYPALSecret);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);
    if (isset($result['access_token'])) {
        return $result['access_token'];
    }
    throw new Exception("Failed to get access token: " . $response);
}

function paypalCreateOrder($params, $saveCard = true) {
    global $gPAYPAL_URL;

    $accessToken = getPaypalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders";
    $currency = $params['currency'] ?? 'JPY';

    // vault設定
    $vaultSettings = [
        "store_in_vault" => $saveCard ? "ON_SUCCESS" : "OFF"
    ];

    $body = [
        "intent" => "CAPTURE",
        "payment_source" => [
            "card" => [
                "attributes" => [
                    "vault" => $vaultSettings,
                    "verification" => [
                        "method" => "SCA_ALWAYS"
                    ]
                ],
                "experience_context" => [
                    "return_url" => $params['return_url'],
                    "cancel_url" => $params['cancel_url'],
                    "shipping_preference" => "NO_SHIPPING"
                ]
            ]
        ],
        "purchase_units" => [[
            "amount" => [
                "currency_code" => $currency,
                "value" => $params['amount']
            ]
        ]]
    ];

    // 既存顧客IDがあれば追加（上書き保存用）
    if (!empty($params['paypal_customer_id'])) {
        $body["customer"] = [
            "id" => $params['paypal_customer_id']
        ];
    }

    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}"
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);
    $result = json_decode($response, true);


      error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($result));


    return $result;
}


function capturePaypalOrder($params) {
    global $gPAYPAL_URL;

    $accessToken = getPaypalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders/{$params['order_id']}/capture";

    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($url));

    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}"
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    $result = json_decode($response, true);
    return $result;
}


function paypalChargeWithVaultId($params) {
    global $gPAYPALClientID, $gPAYPALSecret, $gPAYPAL_URL;

    $accessToken = getPayPalAccessToken();
    $url = $gPAYPAL_URL . "v2/checkout/orders";
    $headers = [
        "Content-Type: application/json",
        "Authorization: Bearer {$accessToken}",
    ];

    $body = [
        "intent" => "CAPTURE",
        "payment_source" => [
            "token" => [
                "id" => $params['vault_id'],
                "type" => "BILLING_AGREEMENT"
            ]
        ],
        "purchase_units" => [[
            "amount" => [
                "currency_code" => $params['currency'],
                "value" => $params['amount']
            ]
        ]]
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);

    $response = curl_exec($ch);
    curl_close($ch);

    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($body));
    error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($response));


    $result = json_decode($response, true);
    return $result;
}

// Create PayPal billing plan for recurring payments
function paypalCreateBillingPlan($planData) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $url = $gPAYPAL_URL . "v1/billing/plans";

        // Build plan name based on plan details
        $planName = $planData['plan_title'];
        if ($planData['term_type'] == 1) {
            $planName .= ' ' . $planData['plan_term'] . '日間';
            $intervalUnit = 'DAY';
            $intervalCount = $planData['plan_term'];
        } else {
            $planName .= ' ' . $planData['plan_term'] . 'ヶ月';
            $intervalUnit = 'MONTH';
            $intervalCount = $planData['plan_term'];
        }

        $body = [
            "product_id" => "HEALMATE_PLAN_" . $planData['plan_id'],
            "name" => $planName,
            "description" => $planData['plan_description'] ?? $planName,
            "status" => "ACTIVE",
            "billing_cycles" => [
                [
                    "frequency" => [
                        "interval_unit" => $intervalUnit,
                        "interval_count" => $intervalCount
                    ],
                    "tenure_type" => "REGULAR",
                    "sequence" => 1,
                    "total_cycles" => 0, // 0 means infinite cycles
                    "pricing_scheme" => [
                        "fixed_price" => [
                            "value" => $planData['plan_price'],
                            "currency_code" => "JPY"
                        ]
                    ]
                ]
            ],
            "payment_preferences" => [
                "auto_bill_outstanding" => true,
                "setup_fee" => [
                    "value" => "0",
                    "currency_code" => "JPY"
                ],
                "setup_fee_failure_action" => "CONTINUE",
                "payment_failure_threshold" => 3
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json",
            "PayPal-Request-Id: " . uniqid()
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingPlan request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingPlan response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'plan_id' => $result['id'],
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing plan creation error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Create PayPal billing agreement from billing plan
function paypalCreateBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $url = $gPAYPAL_URL . "v1/billing/subscriptions";

        // Required parameters
        $planId = $params['plan_id'];
        $subscriberName = $params['subscriber_name'] ?? '';
        $subscriberEmail = $params['subscriber_email'] ?? '';
        $returnUrl = $params['return_url'] ?? '';
        $cancelUrl = $params['cancel_url'] ?? '';

        // Validate required parameters
        if (empty($planId)) {
            throw new Exception("Plan ID is required");
        }

        $body = [
            "plan_id" => $planId,
            "start_time" => date('c', strtotime('+1 minute')), // Start 1 minute from now
            "subscriber" => [
                "name" => [
                    "given_name" => $subscriberName,
                    "surname" => ""
                ],
                "email_address" => $subscriberEmail
            ],
            "application_context" => [
                "brand_name" => "HealMate",
                "locale" => "ja-JP",
                "shipping_preference" => "NO_SHIPPING",
                "user_action" => "SUBSCRIBE_NOW",
                "payment_method" => [
                    "payer_selected" => "PAYPAL",
                    "payee_preferred" => "IMMEDIATE_PAYMENT_REQUIRED"
                ],
                "return_url" => $returnUrl,
                "cancel_url" => $cancelUrl
            ]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json",
            "PayPal-Request-Id: " . uniqid()
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingAgreement request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalCreateBillingAgreement response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'subscription_id' => $result['id'],
                'approval_url' => $result['links'][0]['href'] ?? '', // First link is usually approval URL
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement creation error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Execute/Activate PayPal billing agreement after customer approval
function paypalExecuteBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $subscriptionId = $params['subscription_id'];

        // Validate required parameters
        if (empty($subscriptionId)) {
            throw new Exception("Subscription ID is required");
        }

        // First, activate the subscription
        $activateUrl = $gPAYPAL_URL . "v1/billing/subscriptions/{$subscriptionId}/activate";

        $activateBody = [
            "reason" => "Customer approved subscription"
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($activateUrl);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($activateBody));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $activateResponse = curl_exec($ch);
        $activateHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalExecuteBillingAgreement activate request: '.var_dump_text($activateBody));
        error_log(basename(__FILE__).__LINE__.'|paypalExecuteBillingAgreement activate response: '.var_dump_text($activateResponse));

        // Then get the subscription details
        $getUrl = $gPAYPAL_URL . "v1/billing/subscriptions/{$subscriptionId}";

        $getHeaders = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json"
        ];

        $ch = curl_init($getUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $getHeaders);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPGET, true);

        $getResponse = curl_exec($ch);
        $getHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalExecuteBillingAgreement get response: '.var_dump_text($getResponse));

        $result = json_decode($getResponse, true);

        if ($getHttpCode >= 200 && $getHttpCode < 300) {
            return [
                'success' => true,
                'subscription_id' => $result['id'],
                'status' => $result['status'],
                'billing_agreement_id' => $result['id'], // For compatibility with existing code
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement execution error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

// Charge using existing billing agreement for recurring payments
function paypalChargeWithBillingAgreement($params) {
    global $gPAYPAL_URL;

    try {
        $accessToken = getPaypalAccessToken();
        $billingAgreementId = $params['billing_agreement_id'];
        $amount = $params['amount'];
        $currency = $params['currency'] ?? 'JPY';

        // Validate required parameters
        if (empty($billingAgreementId) || empty($amount)) {
            throw new Exception("Billing agreement ID and amount are required");
        }

        // Create order using billing agreement
        $url = $gPAYPAL_URL . "v2/checkout/orders";

        $body = [
            "intent" => "CAPTURE",
            "payment_source" => [
                "paypal" => [
                    "billing_agreement_id" => $billingAgreementId
                ]
            ],
            "purchase_units" => [[
                "amount" => [
                    "currency_code" => $currency,
                    "value" => $amount
                ]
            ]]
        ];

        $headers = [
            "Content-Type: application/json",
            "Authorization: Bearer {$accessToken}",
            "Accept: application/json",
            "PayPal-Request-Id: " . uniqid()
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log(basename(__FILE__).__LINE__.'|paypalChargeWithBillingAgreement request: '.var_dump_text($body));
        error_log(basename(__FILE__).__LINE__.'|paypalChargeWithBillingAgreement response: '.var_dump_text($response));

        $result = json_decode($response, true);

        if ($httpCode >= 200 && $httpCode < 300) {
            // If order was created successfully, capture it immediately
            $orderId = $result['id'];
            $captureResult = capturePaypalOrder(['order_id' => $orderId]);

            return [
                'success' => true,
                'order_id' => $orderId,
                'capture_data' => $captureResult,
                'data' => $result
            ];
        } else {
            return [
                'success' => false,
                'error' => $result['message'] ?? 'Unknown error',
                'data' => $result
            ];
        }

    } catch (Exception $e) {
        error_log("PayPal billing agreement charge error: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'data' => null
        ];
    }
}

