<?php
	ini_set('error_log', '/var/log/batch.log');
	include("_filepath.inf");
	include_once($gINC.'_global.inc');
	include_once($gLIB.'_pubfunc.inc');
	include_once($gINC.'dao/M20.inc');
	include_once($gINC.'dao/U01.inc');
	include_once($gINC.'dao/U36.inc');
	include_once($gINC.'dao/P01.inc');
	include_once($gINC.'dao/P07.inc');
	include_once($gINC.'dao/S05.inc');
	include_once($gINC.'dao/S06.inc');
	include_once($gINC.'dao/W49.inc');
	include_once($gINC.'logic/paypal.inc');
	include_once($gINC.'logic/mail.inc');
	include_once($gINC.'_mmcrypt.inc');

error_log('batPayPalCharge start');
	// 0:40に起動
	// ----- プラン -----
	$listU01 = $dbU01->U01S24(date('Ymd'));
	foreach ( $listU01 as $dataU01 ) {
		$dataU36 = $dbU36->U36G01($dataU01['user_id'], 2);
		if ( $dataU36['flag_value'] == 1 ) {
			continue;
		}
		// ----- 最終取引がPayPalか確認 -----
		$chkP01 = $dbP01->P01G14($dataU01['user_id'], 1);
		if ( $chkP01['payment_type'] != 7 ) {
			continue;
		}
error_log('user_id:'.$dataU01['user_id']);
		$errMessage = '';
		// ----- 更新時プラン変更 -----
		if ( !empty($dataU01['renewal_plan_id']) ) {
			$dataU01['plan_id'] = $dataU01['renewal_plan_id'];
			$dataM20 = $dbM20->M20G01($dataU01['renewal_plan_id']);
			$dataU01['plan_price'] = $dataM20['plan_price'];
			$dataU01['plan_title'] = $dataM20['plan_title'];
			$dataU01['term_type'] = $dataM20['term_type'];
			$dataU01['plan_term'] = $dataM20['plan_term'];
		}
		// ----- P01作成 -----
		$dataP01 = $dbP01->CreateNewDto();
		$dataP01['user_id'] = $dataU01['user_id'];
		$dataP01['payment_type'] = 7;
		$dataP01['plan_id'] = $dataU01['plan_id'];
		$dataP01['request_date_time'] = date('YmdHis');
		$dataP01['price'] = $dataU01['plan_price'];
		$dataP01 = $dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
		$enc = new MMCrypt();
		$dataP01['order_code'] = $enc->MMEncode(12, $dataP01['order_id']);
		$dataP01 = $dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
		$dataU01['order_id'] = $dataP01['order_id'];

		$item_name = $dataU01['plan_title'];
		if ( $dataU01['term_type'] == 1 ) {
			$item_name .= ' ' . $dataU01['plan_term'] . '日間';
		} else {
			$item_name .= ' ' . $dataU01['plan_term'] . 'ヶ月';
		}

		$dataP01['tran_date_time'] = date('YmdHis');
		$dataP07 = $dbP07->CreateNewDto();

		// Old code: Get paypal token
		//$dataP07['paypal_token'] = $dataU01['paypal_token'];
		//$params = [
		//	'vault_id' => $dataP07['paypal_token'],
		//	'amount' => $dataP01['price'],
		//	'currency' => 'JPY',
		//];

		//$paymentData = paypalChargeWithVaultId($params);
		//error_log(basename(__FILE__).__LINE__.'|'.var_dump_text($paymentData));



		// New code: pull payment with billing agreement id (similiar to vault id above)
		//$params = [
		//	'billing_agreement_id' => $dataU01['paypal_billing_agreement_id'],
		//	'amount' => $dataP01['price'],
		//	'currency' => 'JPY',
		//];
		//$paymentData = paypalExecuteBillingAgreement($params);




		// list($httpcode, $resultData) = $PayPal->PayPalPayment($dataU01, $dataP01, $dataP07, $item_name);
		// if ( $httpcode == 200 && $resultData['status'] == 'authorized' ) {
		// 	list($httpcode, $resultData) = $PayPal->PayPalCapture($resultData['id'], $dataP01);
		// 	if ( $httpcode == 200 && $resultData['status'] == 'closed' ) {
		// 		$dataP01['payment_result'] = 1;
		// 		if ( $dataU01['term_type'] == 1 ) {
		// 			$dataP01['plan_expiry_date'] = date('Ymd', mktime(0, 0, 0, date('m'), date('d') + $dataU01['plan_term'], date('Y')));
		// 		} else {
		// 			$dataP01['plan_expiry_date'] = date('Ymd', mktime(0, 0, 0, date('m') + $dataU01['plan_term'], date('d'), date('Y')));
		// 		}
		// 		$dataP01['tran_id'] = $resultData['id'];
		// 		$dataP01['receivable_balance'] = $dataP01['price'];
		// 		// ----- U01更新 -----
		// 		$dataU01['plan_expiry_date'] = $dataP01['plan_expiry_date'];
		// 		$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
		// 	} else {
		// 		$dataP01['payment_result'] = 9;
		// 		$dataP01['err_code'] = $resultData['code'];
		// 		$dataP01['err_info'] = $resultData['description'];
		// 	}
		// } else {
		// 	$dataP01['payment_result'] = 9;
		// 	$dataP01['err_code'] = $resultData['code'];
		// 	$dataP01['err_info'] = $resultData['description'];
		// }
		
		$dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
		if ( $dataP01['payment_result'] == 9 ) {
			// ----- 有効期限切れ -----
			if ( $dataU01['user_sex'] == 1 ) {
				// ----- 男性は無料プランへ（有効期限なし） -----
				$dataU01['plan_id'] = 0;
				$dataU01['plan_expiry_date'] = 0;
				// ----- W49（猶予期間登録） -----
				$dataW49 = $dbW49->CreateNewDto();
				$dataW49['deprivation_date'] = date('Ymd', mktime(0, 0, 0, date('m'), date('d') + 2, date('Y')));
				$dataW49['user_id'] = $dataU01['user_id'];
				$dbW49->W49U01($dataW49, basename(__FILE__, ".php"));
			} else if ( $dataU01['user_sex'] == 2 ) {
				// ----- 女性はスタンダードプランへ（有効期限なし） -----
				$dataM20 = $dbM20->M20G03();
				$dataU01['plan_id'] = $dataM20['plan_id'];
				$dataU01['plan_expiry_date'] = 0;
				// ----- プレミアムマーク取消 -----
				$dataU3606 = $dbU36->U36G01($dataU01['user_id'], 6);
				if ( !empty($dataU3606['flag_value']) ) {
					$dataU3606['flag_value'] = 0;
					$dbU36->U36U01($dataU3606, basename(__FILE__, ".php"));
				}
			}
			$dbU01->U01U02($dataU01, basename(__FILE__, ".php"));
			// ----- メール配信 -----
			$mail = new autoMail();
			$mail->PayPalNGMail($dataU01);
		}
	}





// 	// ----- サービス、継続課金 -----
// 	$listS05 = $dbS05->S05S04(date('Ymd'), 7);
// 	foreach ( $listS05 as $dataS05 ) {
// error_log('service user_id:'.$dataS05['user_id']);
// 		// ----- P01作成 -----
// 		$dataP01 = $dbP01->CreateNewDto();
// 		$dataP01['user_id'] = $dataS05['user_id'];
// 		$dataP01['payment_type'] = 7;
// 		$dataP01['sales_item'] = 3;
// 		$dataP01['request_date_time'] = date('YmdHis');
// 		$dataP01['price'] = $dataS05['price'];
// 		$dataP01 = $dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
// 		$enc = new MMCrypt();
// 		$dataP01['order_code'] = $enc->MMEncode(12, $dataP01['order_id']);
// 		$dataP01 = $dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
// 		$PayPal = new payPayPal();
// 		$dataP01['tran_date_time'] = date('YmdHis');
// 		$chkP07 = $dbP07->P07G02($dataS05['user_id']);
// 		$dataP07 = $dbP07->CreateNewDto();
// 		$dataP07['paypal_token'] = $chkP07['paypal_token'];
// 		$dataS05['Idate'] = $dataS05['U01_Idate'];
// 		list($httpcode, $resultData) = $PayPal->PayPalPayment($dataS05, $dataP01, $dataP07, $dataS05['menu_name']);
// 		if ( $httpcode == 200 && $resultData['status'] == 'authorized' ) {
// 			list($httpcode, $resultData) = $PayPal->PayPalCapture($resultData['id'], $dataP01);
// 			if ( $httpcode == 200 && $resultData['status'] == 'closed' ) {
// 				$dataP01['payment_result'] = 1;
// 				$dataP01['tran_id'] = $resultData['id'];
// 				$dataP01['receivable_balance'] = $dataP01['price'];
// 				// ----- S05更新 -----
// 				$dataS05['service_expiry_date'] = date('Ymd', mktime(0, 0, 0, date('m') + $dataS05['subscription_cycle'], date('d'), date('Y')));
// 				$dbS05->S05U01($dataS05, basename(__FILE__, ".php"));
// 				// ----- S06 -----
// 				$dataS06 = $dbS06->CreateNewDto();
// 				$dataS06['reserve_id'] = $dataS05['reserve_id'];
// 				$dataS06['order_id'] = $dataP01['order_id'];
// 				$dbS06->S06U01($dataS06, basename(__FILE__, ".php"));
// 			} else {
// 				$dataP01['payment_result'] = 9;
// 				$dataP01['err_code'] = $resultData['code'];
// 				$dataP01['err_info'] = $resultData['description'];
// 			}
// 		} else {
// 			$dataP01['payment_result'] = 9;
// 			$dataP01['err_code'] = $resultData['code'];
// 			$dataP01['err_info'] = $resultData['description'];
// 		}
// 		$dbP01->P01U01($dataP01, basename(__FILE__, ".php"));
// 		if ( $dataP01['payment_result'] == 9 ) {
// 			$dataS05['reserve_status'] = 3;
// 			$dataS05['status_date'] = date('Ymd');
// 			$dbS05->S05U01($dataS05, basename(__FILE__, ".php"));
// 			// ----- 有効期限切れ -----
// 			// ----- メール配信 -----
// 			$mail = new autoMail();
// 			$mail->PayPalServiceNGMail($dataU01);
// 		}
// 	}
error_log('batPayPalCharge finish');
	exit();
